# AI Tool Calling Framework

This document explains how to use and extend the AI tool calling framework in Wynnutils, similar to the Python LM Studio example you provided.

## Overview

The framework provides:
- **Easy tool registration** with annotations
- **Automatic parameter handling** and validation
- **JSON serialization/deserialization** of tool results
- **Integration with existing ConversationManager**
- **HTTP request capabilities** for external APIs

## Quick Start

### 1. Create a Tool

```java
package com.ifallious.utils.ai.tools.impl;

import com.ifallious.utils.ai.tools.*;

public class WeatherTool implements Tool {
    @Override
    public String getName() {
        return "get_weather";
    }

    @Override
    public String getDescription() {
        return "Get current weather information for a specified location";
    }

    @ToolExecute
    public String execute(
            @ToolParameter(
                    name = "location",
                    type = "string",
                    description = "The city or location to get weather for",
                    required = true
            ) String location,
            
            @ToolParameter(
                    name = "units",
                    type = "string", 
                    description = "Temperature units: 'celsius' or 'fahrenheit'",
                    required = false
            ) String units
    ) {
        // Your tool implementation here
        Map<String, Object> result = new HashMap<>();
        result.put("status", "success");
        result.put("location", location);
        result.put("temperature", "22°C");
        result.put("condition", "Sunny");
        
        return new Gson().toJson(result);
    }
}
```

### 2. Register the Tool

Add your tool to `ToolManager.java`:

```java
public static void initializeTools() {
    // ... existing tools ...
    ToolRegistry.registerTool(new WeatherTool());
}
```

### 3. Use with AI

```java
// Create conversation manager with tools enabled
ToolCallingConversationManager manager = ToolCallingConversationManager.create(500)
    .withToolsEnabled(true);

// Send message - tools will be called automatically if needed
manager.sendMessage("What's the weather like in New York?")
    .thenAccept(response -> {
        UChat.chat("AI Response: " + response);
    });
```

## Framework Components

### Core Classes

- **`Tool`** - Interface that all tools must implement
- **`ToolRegistry`** - Central registry for managing tools
- **`ToolManager`** - Initializes and registers built-in tools
- **`ToolCallingConversationManager`** - Enhanced conversation manager with tool support

### Annotations

- **`@ToolExecute`** - Marks the method to execute when tool is called
- **`@ToolParameter`** - Defines parameters with type information for AI

### Parameter Types

Supported parameter types for `@ToolParameter`:
- `"string"` - Text values
- `"number"` - Numeric values (int, double, etc.)
- `"boolean"` - True/false values
- `"object"` - Complex JSON objects
- `"array"` - Lists/arrays

## Built-in Tools

### WikipediaTool
- **Name**: `fetch_wikipedia_content`
- **Purpose**: Search and fetch Wikipedia articles
- **Parameters**: `search_query` (string)
- **Returns**: JSON with article title and content

### MinecraftInfoTool
- **Name**: `get_minecraft_info`
- **Purpose**: Get current game state information
- **Parameters**: `info_type` (string: "position", "health", "dimension", "all")
- **Returns**: JSON with requested game information

## Advanced Usage

### HTTP Requests in Tools

```java
@ToolExecute
public String execute(@ToolParameter(...) String query) {
    HttpClient client = HttpClient.newHttpClient();
    HttpRequest request = HttpRequest.newBuilder()
        .uri(URI.create("https://api.example.com/data?q=" + query))
        .build();
        
    try {
        HttpResponse<String> response = client.send(request, 
            HttpResponse.BodyHandlers.ofString());
        return response.body();
    } catch (Exception e) {
        return createErrorResult("API request failed: " + e.getMessage());
    }
}
```

### Error Handling

Always return JSON with status information:

```java
private String createErrorResult(String message) {
    Map<String, Object> result = new HashMap<>();
    result.put("status", "error");
    result.put("message", message);
    return new Gson().toJson(result);
}

private String createSuccessResult(Object data) {
    Map<String, Object> result = new HashMap<>();
    result.put("status", "success");
    result.put("data", data);
    return new Gson().toJson(result);
}
```

### Complex Parameters

```java
@ToolExecute
public String execute(
    @ToolParameter(
        name = "config",
        type = "object",
        description = "Configuration object with settings"
    ) Map<String, Object> config
) {
    // Access nested properties
    String mode = (String) config.get("mode");
    Integer timeout = ((Double) config.get("timeout")).intValue();
    // ... use parameters
}
```

## Integration with Existing Code

The framework integrates seamlessly with your existing AI setup:

```java
// In AskCommand.java or similar
ToolCallingConversationManager conversation = ToolCallingConversationManager
    .create(100)
    .withToolsEnabled(true)
    .system("You are a helpful assistant with access to tools.");

conversation.sendMessage(userMessage).thenAccept(response -> {
    // Tools are automatically called and results incorporated
    Utils.sendCommand("g " + response);
});
```

## Best Practices

1. **Tool Names**: Use descriptive, snake_case names
2. **Descriptions**: Be specific about what the tool does and when to use it
3. **Parameters**: Always validate required parameters
4. **Error Handling**: Return structured JSON with status information
5. **Logging**: Use `Wynnutils.LOGGER` for debugging
6. **Timeouts**: Set reasonable timeouts for HTTP requests
7. **Thread Safety**: Tools may be called concurrently

## Comparison to Python Example

This Java framework provides the same functionality as your Python example:

| Python | Java |
|--------|------|
| `def fetch_wikipedia_content(search_query: str) -> dict:` | `@ToolExecute public String execute(@ToolParameter(...) String searchQuery)` |
| `WIKI_TOOL = {"type": "function", ...}` | `@ToolParameter` annotations auto-generate this |
| `client.chat.completions.create(tools=[WIKI_TOOL])` | `ToolRegistry.getToolsForAI()` provides tools |
| Manual tool call parsing | Automatic tool call handling |
| `json.loads(tool_call.function.arguments)` | Automatic parameter conversion |

The Java framework handles the complexity automatically while providing the same flexibility and power.
