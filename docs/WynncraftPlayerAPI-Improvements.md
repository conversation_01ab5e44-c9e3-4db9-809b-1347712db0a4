# WynncraftPlayerAPI Tool Improvements

## Overview

The WynncraftPlayerAPI tool has been enhanced to allow the AI to specify exactly what information it wants from the Wynncraft API response, rather than always returning the complete response. This improvement makes the tool more efficient and allows for more targeted data retrieval.

## New Features

### Selective Information Retrieval

The tool now accepts a new optional parameter `requested_info` that allows the AI to specify which fields it wants from the API response.

#### Parameter Details

- **Name**: `requested_info`
- **Type**: `string`
- **Required**: `false`
- **Description**: Specific information to retrieve. Use comma-separated field names, dot notation for nested fields (e.g., 'globalData.wars'), or 'all' for everything

### Usage Examples

#### Basic Field Selection
```json
{
  "username": "Salted",
  "requested_info": "username,online,server"
}
```
Returns only the username, online status, and server information.

#### Nested Field Access
```json
{
  "username": "Salted", 
  "requested_info": "guild.name,globalData.wars,globalData.totalLevels"
}
```
Returns specific nested fields like guild name and global statistics.

#### Multiple Field Types
```json
{
  "username": "Salted",
  "requested_info": "rank,guild,characters"
}
```
Returns the player's rank, complete guild information, and all character data.

#### Complete Information
```json
{
  "username": "Salted",
  "requested_info": "all"
}
```
Returns the complete API response (same as not specifying the parameter).

## Available Information Fields

### Basic Player Info
- `username` - Player's username
- `online` - Whether the player is currently online
- `server` - Current server (if online)
- `activeCharacter` - Currently active character UUID
- `uuid` - Player's UUID

### Rank and Status
- `rank` - Player's rank
- `rankBadge` - URL to rank badge SVG
- `legacyRankColour` - Rank color information
- `shortenedRank` - Shortened rank name
- `supportRank` - Support rank if applicable
- `veteran` - Whether player is a veteran

### Activity Data
- `firstJoin` - First join date
- `lastJoin` - Last join date
- `playtime` - Total playtime

### Guild Information
- `guild` - Complete guild data including:
  - `guild.name` - Guild name
  - `guild.prefix` - Guild prefix
  - `guild.rank` - Player's rank in guild
  - `guild.rankStars` - Rank stars

### Global Statistics
- `globalData` - Complete global statistics including:
  - `globalData.wars` - Total wars participated in
  - `globalData.totalLevels` - Sum of all character levels
  - `globalData.killedMobs` - Total mobs killed
  - `globalData.chestsFound` - Total chests found
  - `globalData.dungeons` - Dungeon completion data
  - `globalData.raids` - Raid completion data
  - `globalData.completedQuests` - Number of completed quests
  - `globalData.pvp` - PvP statistics

### Other Data
- `forumLink` - Forum profile link
- `ranking` - Current rankings
- `previousRanking` - Previous rankings
- `publicProfile` - Whether profile is public
- `characters` - Complete character information

## Implementation Details

### Filtering Logic

The tool uses a JSON parsing approach to extract only the requested fields from the full API response:

1. **Field Parsing**: Comma-separated field names are split and processed individually
2. **Nested Access**: Dot notation (e.g., `guild.name`) allows access to nested JSON objects
3. **Structure Preservation**: The filtered response maintains the original JSON structure
4. **Error Handling**: Invalid field paths are silently ignored

### Backward Compatibility

The improvement is fully backward compatible:
- If no `requested_info` parameter is provided, the tool returns the complete API response
- Existing AI prompts and tool calls continue to work unchanged
- The parameter is optional, so existing integrations are not affected

## Testing

### Command Line Testing

The tool can be tested using the following commands:

```
/wynnutils AI tools test-wynncraft
```

This command tests the improved functionality with:
1. Basic field filtering (`username,online,server`)
2. Nested field access (`guild.name,globalData.wars`)

### Direct Tool Testing

```
/wynnutils AI tools test-direct
```

This includes tests for the Wynncraft Player API with both filtered and complete responses.

### Integration Testing

```
/wynnutils AI tools test
```

This runs the full AI conversation tests, including the Wynncraft Player API tool.

## Benefits

1. **Efficiency**: Reduces response size by returning only requested data
2. **Clarity**: AI gets exactly the information it needs without parsing large responses
3. **Performance**: Smaller responses are faster to process and transmit
4. **Flexibility**: Supports both simple and complex data retrieval patterns
5. **Maintainability**: Clean separation between data fetching and filtering logic

## Error Handling

The tool includes comprehensive error handling:
- Network errors during API calls are caught and returned as error responses
- JSON parsing errors are handled gracefully
- Invalid field paths are ignored without causing failures
- All errors include descriptive messages for debugging

## Future Enhancements

Potential future improvements could include:
- Array filtering for character-specific data
- Conditional filtering based on field values
- Response caching for frequently requested data
- Support for multiple player queries in a single call
