# ConversationManager - Unified AI Chat Interface

This document describes how to use the ConversationManager to interact with AI models through a unified API, and how to add new tools for tool-calling.

## Overview

ConversationManager abstracts AI provider selection and presents a single interface to send messages and receive responses. It currently routes to the Local client (http://127.0.0.1:1234), but is designed to support external providers (OpenAI, Anthropic) in the future.

Key features:
- Simple, fluent configuration
- Conversation state and message history management
- Streaming and non-streaming responses
- Tool calling support (OpenAI-compatible)

## Quick Start

```java
ConversationManager chat = new ConversationManager()
    .withProvider("local")
    .withModel("openai/gpt-oss-20b")
### Async usage

Use async methods to avoid blocking the main game thread:

```java
chat.sendMessageAsync("Hello").thenAccept(resp -> {
    // update UI thread-safe
});

chat.sendMessageStreamingAsync("Introduce yourself.", new StreamHandler() {
    public void onDelta(String delta) { /* schedule UI update on render thread */ }
});
```

### Timeouts

```java
new ConversationManager()
  .withConnectTimeoutSec(5)
  .withReadTimeoutSec(45);
```

    .withTemperature(0.7)
    .withToolsEnabled(true);

String response = chat.sendMessage("Hello, how are you?");
```

### Streaming

```java
String finalText = chat.sendMessageStreaming("Introduce yourself.", new StreamHandler() {
    public void onDelta(String delta) { /* append to UI */ }
    public void onComplete(String full) { /* finalize UI */ }
    public void onError(Exception e) { /* handle errors */ }
});
```

## Configuration Parameters

- provider: "local" | "openai" | "anthropic" (external providers are placeholders for now)
- model: model name, e.g., "openai/gpt-oss-20b"
- endpoint: API URL for local models (default: http://127.0.0.1:1234/v1/chat/completions)
- temperature: 0.0 to 1.0 (default 0.7)
- maxTokens: integer, -1 for unlimited (server-dependent)
- toolsEnabled: boolean flag to enable tool calling
- tools: optional custom tool specs (OpenAI-compatible JSON structures)

## Tool Calling

When tools are enabled, the Local client sends an OpenAI-compatible `tools` array along with messages. If the model returns `tool_calls`, the manager will:
1. Execute each tool locally
2. Append a `tool` role message with the tool's result (linked by `tool_call_id`)
3. Perform a follow-up completion so the model can incorporate tool outputs

### Built-in Tool: search_products

The tool is described as follows:

```json
{
  "type": "function",
  "function": {
    "name": "search_products",
    "description": "Search the product catalog by various criteria. Use this whenever a customer asks about product availability, pricing, or specifications.",
    "parameters": {
      "type": "object",
      "properties": {
        "query": { "type": "string", "description": "Search terms or product name" },
        "category": { "type": "string", "description": "Product category to filter by", "enum": ["electronics", "clothing", "home", "outdoor"] },
        "max_price": { "type": "number", "description": "Maximum price in dollars" }
      },
      "required": ["query"],
      "additionalProperties": false
    }
  }
}
```

### Adding a New Tool

1. Define its JSON schema and add it to the list returned by `Tools.getToolSpecs()`.
2. Implement execution logic inside `Tools.execute(name, jsonArgs)`.
3. Enable tools via `withToolsEnabled(true)` or pass custom specs via `withTools(...)`.

Example snippet (adding a hypothetical `get_weather`):

```java
// In Tools.getToolSpecs():
Map<String, Object> getWeatherFn = new HashMap<>();
getWeatherFn.put("name", "get_weather");
getWeatherFn.put("description", "Get current weather for a city.");
Map<String, Object> params = new HashMap<>();
params.put("type", "object");
params.put("properties", Map.of("city", Map.of("type", "string")));
params.put("required", List.of("city"));
params.put("additionalProperties", false);
Map<String, Object> weatherTool = new HashMap<>();
weatherTool.put("type", "function");
weatherTool.put("function", parametersWithName(getWeatherFn, params));

// In Tools.execute():
if ("get_weather".equals(name)) {
    // parse jsonArgs and return a JSON string with the result
}
```

## Error Handling

ConversationManager follows the project’s pattern by catching runtime exceptions, reporting via ErrorReporter, and returning a concise error string (prefixed with `[AI error]`).

## Extensibility

- External providers: create a client that mirrors Local’s API and update ConversationManager to route based on provider.
- Custom tools: define schemas and add execution logic in `Tools`.
- Conversation access: you can inspect or clear history via `getHistory()` and `clear()`.

