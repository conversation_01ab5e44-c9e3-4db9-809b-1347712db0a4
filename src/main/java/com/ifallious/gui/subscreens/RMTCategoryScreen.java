package com.ifallious.gui.subscreens;

import com.ifallious.gui.components.ConfigOption;
import gg.essential.elementa.UIComponent;

public class RMTCategoryScreen extends CategoryConfigScreen {
    public RMTCategoryScreen(UIComponent parent, <PERSON><PERSON><PERSON> first) {
        super(parent, "RMT Category", first);
    }

    @Override
    protected void initOptions() {
        addOption(new ConfigOption(
                "Rare Mob Notification",
                "rareMobNotification",
                "Sends a Message in Chat when a rare mob is found.",
                true
        ));
        addOption(new ConfigOption(
                "Key Guardian Notification",
                "keyGuardianNotification",
                "Sends a Message in Chat when a key guardian is found.",
                false
        ));

    }
}
