package com.ifallious.gui.subscreens;

import com.ifallious.gui.components.ConfigOption;
import gg.essential.elementa.UIComponent;

public class AICategoryScreen extends CategoryConfigScreen{

    /**
     * Creates a new CategoryConfigScreen with the given parent component and title.
     *
     * @param parent The parent component to attach to
     * @param first
     */
    public AICategoryScreen(UIComponent parent, <PERSON><PERSON><PERSON> first) {
        super(parent, "AI", first);
    }

    @Override
    protected void initOptions() {
        addOption(new ConfigOption(
                "AI Endpoint URL",
                "AIEndpoint",
                "Sets the Endpoint the AI contacts (only works for local Models with a Open-AI like api)",
                null,
                2
        ));
        addOption(new ConfigOption(
                "AI Model",
                "AIModel",
                "Sets which AI Model to use",
                null,
                2
        ));
        addOption(new ConfigOption(
                "System Prompt",
                "AISystemPrompt",
                "Prompt given to the AI that tells it how to behave",
                null,
                7
        ));
        addOption(new ConfigOption(
                "Temperature",
                "AITemperature",
                "Influences AI Output",
                0.7,
                0,
                1.0
        ));
        addOption(new ConfigOption(
                "Tool Calls",
                "AITools",
                "Allows the AI to use tools like the Wynncraft api",
                true
        ));
        addOption(new ConfigOption(
                "Respond to Guild Messages",
                "guildResponse",
                "lets AI respond to people saying !ask in guild chat",
                false
        ));
    }
}
