package com.ifallious.gui.subscreens;

import com.ifallious.gui.components.ConfigOption;
import gg.essential.elementa.UIComponent;

public class SpellMacroCategoryScreen extends CategoryConfigScreen {
    public SpellMacroCategoryScreen(UIComponent parent, <PERSON>olean first) {
        super(parent, "Spell Macro Category", first);
    }

    @Override
    protected void initOptions() {
        addOption(new ConfigOption(
                "Spell Macro Hold",
                "spellMacroHold",
                "When enabled, spells will be cast continuously while the key is held down. When disabled, pressing the key once will add a single spell to the queue.",
                false
        ));
        addOption(new ConfigOption(
                "Corrupted Cooldown",
                "corruptedCooldown",
                "Shows a notification when your corrupted cooldown is about to end.",
                true
        ));
        addOption(new ConfigOption(
                "Recast Totem",
                "recastTotem",
                "Automatically recasts your totem when it is about to expire.",
                false
        ));
        addOption(new ConfigOption(
                "Auto Arcane Transfer",
                "autoTransfer",
                "Automatically casts Arcane Transfer at a set Mana Bank amount.",
                false
        ));
        addOption(new ConfigOption(
                "Arcane Transfer Threshold",
                "autoTransferThreshold",
                "Minimum value to cast arcane transfer at depending on Aspects and other factors",
                150,
                1,
                180
        ));
        addOption(new ConfigOption(
                "Shadow Projection",
                "shadowProjection",
                "Automatically handles your shadow projection.",
                false
        ));
    }
}
