package com.ifallious.gui.subscreens;

import com.ifallious.gui.components.ConfigOption;
import gg.essential.elementa.UIComponent;

public class RaidCategoryScreen extends CategoryConfigScreen{
    public RaidCategoryScreen(UIComponent parent, <PERSON><PERSON><PERSON> first) {
        super(parent, "Raid Category", first);
    }

    @Override
    protected void initOptions() {
        addOption(new ConfigOption(
                "TCC Exit Finder",
                "tccExitLocator",
                "Attempts to Locate the Exit in the TCC Maze",
                true
        ));
    }
}
