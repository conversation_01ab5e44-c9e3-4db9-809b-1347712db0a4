package com.ifallious.gui.screens;

import com.ifallious.features.spells.Spellmacro;
import com.ifallious.gui.GUIUtils;
import com.ifallious.gui.components.MultiLineTextComponent;
import com.ifallious.gui.components.NumberInput;
import com.ifallious.gui.components.RoundedRectangleWithBorder;
import com.ifallious.gui.components.SpellInput;
import com.ifallious.utils.ErrorReporter;
import gg.essential.elementa.ElementaVersion;
import gg.essential.elementa.UIComponent;
import gg.essential.elementa.WindowScreen;
import gg.essential.elementa.components.*;
import gg.essential.elementa.components.input.UITextInput;
import gg.essential.elementa.constraints.*;
import gg.essential.elementa.constraints.animation.Animations;
import gg.essential.elementa.effects.ScissorEffect;
import gg.essential.universal.UChat;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.awt.*;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

import static com.ifallious.features.spells.Spellmacro.*;
import static com.ifallious.gui.GUIUtils.*;

public class EditorGUI extends WindowScreen {
    public float scalingFactor = getScale();
    private final float maincontainerpadding = 6 * scalingFactor;
    private final float borderwidth = 2;
    private String selectedSpell = "";
    private static List<UIComponent> allItems = new ArrayList<>();

    UIComponent background = new RoundedRectangleWithBorder(10f, getWindow(), new CenterConstraint(), new CenterConstraint(), 1000f * scalingFactor + maincontainerpadding * 2, 700f * scalingFactor + maincontainerpadding * 2, borderwidth, new Color(150, 100, 255, 230), new Color(20, 22, 36, 230));
    UIComponent container = new UIContainer()
            .setX(new CenterConstraint())
            .setY(new CenterConstraint())
            .setWidth(new PixelConstraint(1000f * scalingFactor))
            .setHeight(new PixelConstraint(700f * scalingFactor))
            .setChildOf(background);
    UIComponent sidebar = new UIRoundedRectangle(10f)
            .setX(new SiblingConstraint(20f * scalingFactor))
            .setY(new PixelConstraint(0f))
            .setWidth(new PixelConstraint(200f * scalingFactor))
            .setHeight(new PixelConstraint(700f * scalingFactor))
            .enableEffect(new ScissorEffect())
            .setColor(new Color(20, 22, 36, 230))
            .setChildOf(container);
    UIComponent content = new UIRoundedRectangle(10f)
            .setX(new SiblingConstraint(20f * scalingFactor))
            .setY(new PixelConstraint(0f))
            .setWidth(new PixelConstraint(780f * scalingFactor))
            .setHeight(new PixelConstraint(700f * scalingFactor))
            .enableEffect(new ScissorEffect())
            .setColor(new Color(20, 22, 36, 230))
            .setChildOf(container);

    public EditorGUI() {
        super(ElementaVersion.V8, true, false, false, 1);
        try {
            createSidebar();
            updateContent();
            fadeIn(background, 0.1f);
            fadeIn(sidebar, 0.3f);
            fadeIn(content, 0.2f);
        } catch (Exception e) {
            ErrorReporter.reportError("EditorGUI initialization failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
        }
    }
    private void createSidebar() {
        UIComponent header = new UIContainer()
                .setX(new CenterConstraint())
                .setY(new PixelConstraint(0f))
                .setWidth(new PixelConstraint(180f * scalingFactor))
                .setHeight(new PixelConstraint(40f * scalingFactor))
                .setChildOf(sidebar);
        UIComponent title = new UIText("Spellcycles:", false)
                .setX(new CenterConstraint())
                .setY(new CenterConstraint())
                .setTextScale(new PixelConstraint(2f * scalingFactor))
                .setChildOf(header);
        UIComponent line = new UIRoundedRectangle(5f)
                .setX(new CenterConstraint())
                .setY(new SiblingConstraint(4f * scalingFactor))
                .setWidth(new PixelConstraint(140f * scalingFactor))
                .setHeight(new PixelConstraint(2f * scalingFactor))
                .setColor(new Color(150, 100, 255, 200))
                .setChildOf(sidebar);
        UIComponent sidebarItems = new ScrollComponent()
                .setX(new CenterConstraint())
                .setY(new SiblingConstraint(8f * scalingFactor))
                .setWidth(new PixelConstraint(180f * scalingFactor))
                .setHeight(new PixelConstraint(590f * scalingFactor))
                .setChildOf(sidebar);
        UIComponent AddButton = new UIRoundedRectangle(5f)
                .setX(new CenterConstraint())
                .setY(new SiblingConstraint(15f * scalingFactor))
                .setWidth(new PixelConstraint(100f * scalingFactor))
                .setHeight(new PixelConstraint(30f * scalingFactor))
                .setColor(new Color(50, 200, 50, 200))
                .setChildOf(sidebar);
        appear(AddButton, 0.3f);
        UIComponent AddButtonIcon = UIImage.ofResource("/assets/wynnutils/plus.png")
                .setX(new PixelConstraint(3f * scalingFactor))
                .setY(new CenterConstraint())
                .setWidth(new PixelConstraint(20f * scalingFactor))
                .setHeight(new PixelConstraint(20f * scalingFactor))
                .setChildOf(AddButton);
        UIComponent AddButtonText = new UIText("Add Cycle", false)
                .setX(new SiblingConstraint(3f * scalingFactor))
                .setY(new CenterConstraint())
                .setWidth(new ScaledTextConstraint(1.3f * scalingFactor))
                .setHeight(new ScaledTextConstraint(1.3f * scalingFactor))
                .setChildOf(AddButton);
        AddButton.onMouseClick((component, event) -> {
            boolean nameExists = java.util.Arrays.asList(Spellmacro.getCycleNames()).contains("Empty Cycle");
            if (!nameExists) {
                Spellmacro.addCycle("Empty Cycle", new CycleConfig(new String[][] {}, 10, 0));
                sidebarItems.clearChildren();
                addCyclesToSidebar(sidebarItems);
            } else {
                UChat.chat("You already have a empty cycle!");
            }
            return null;
        });
        addCyclesToSidebar(sidebarItems);
    }
    private void addCyclesToSidebar(UIComponent sidebar) {
        String[] cycles = Spellmacro.getCycleNames();
        for (String cycle : cycles) {
            SidebarItem sidebarItem = new SidebarItem(5f, cycle, sidebar);
        }
    }
    private void updateContent() {
        content.clearChildren();
        if (selectedSpell == "") {
            // Expanded Tutorial UI when no spell is selected
            UIComponent tutorialContainer = new UIContainer()
                .setX(new CenterConstraint())
                .setY(new CenterConstraint())
                .setWidth(new PixelConstraint(700f * scalingFactor))
                .setHeight(new PixelConstraint(540f * scalingFactor))
                .setChildOf(content);

                // Title
                UIComponent tutorialTitle = new UIText("Welcome to the Spellcycle Editor!", false)
                    .setX(new CenterConstraint())
                    .setY(new PixelConstraint(20f * scalingFactor))
                    .setTextScale(new PixelConstraint(2.5f * scalingFactor))
                    .setChildOf(tutorialContainer);

                // Section 1: Sidebar & Selection
                UIComponent section1 = new UIText("1. Selecting and Managing Spellcycles", false)
                    .setX(new PixelConstraint(40f * scalingFactor))
                    .setY(new SiblingConstraint(35f * scalingFactor))
                    .setTextScale(new PixelConstraint(1.7f * scalingFactor))
                    .setChildOf(tutorialContainer);
                UIComponent section1a = new UIText("• Select a spellcycle from the sidebar to view or edit it.", false)
                    .setX(new PixelConstraint(60f * scalingFactor))
                    .setY(new SiblingConstraint(10f * scalingFactor))
                    .setTextScale(new PixelConstraint(1.3f * scalingFactor))
                    .setChildOf(tutorialContainer);
                UIComponent section1b = new UIText("• Use the plus button to add a new spellcycle.", false)
                    .setX(new PixelConstraint(60f * scalingFactor))
                    .setY(new SiblingConstraint(6f * scalingFactor))
                    .setTextScale(new PixelConstraint(1.3f * scalingFactor))
                    .setChildOf(tutorialContainer);

                // Section 2: Editing and Deleting
                UIComponent section2 = new UIText("2. Editing and Deleting", false)
                    .setX(new PixelConstraint(40f * scalingFactor))
                    .setY(new SiblingConstraint(18f * scalingFactor))
                    .setTextScale(new PixelConstraint(1.7f * scalingFactor))
                    .setChildOf(tutorialContainer);
                UIComponent section2a = new UIText("• For a spellcycle: Right-click a spellcycle in the sidebar to rename or delete it.", false)
                    .setX(new PixelConstraint(60f * scalingFactor))
                    .setY(new SiblingConstraint(10f * scalingFactor))
                    .setTextScale(new PixelConstraint(1.3f * scalingFactor))
                    .setChildOf(tutorialContainer);
                UIComponent section2b = new UIText("• For individual spells: Right-click a spell in the main area to edit or delete it.", false)
                    .setX(new PixelConstraint(60f * scalingFactor))
                    .setY(new SiblingConstraint(6f * scalingFactor))
                    .setTextScale(new PixelConstraint(1.3f * scalingFactor))
                    .setChildOf(tutorialContainer);

                // Section 3: Spellcycle Structure
                UIComponent section3 = new UIText("3. Spellcycle Structure", false)
                    .setX(new PixelConstraint(40f * scalingFactor))
                    .setY(new SiblingConstraint(18f * scalingFactor))
                    .setTextScale(new PixelConstraint(1.7f * scalingFactor))
                    .setChildOf(tutorialContainer);
                UIComponent section3a = new UIText("• Each spell in a cycle should be a short sequence, like R,R,R or L,L,L.", false)
                    .setX(new PixelConstraint(60f * scalingFactor))
                    .setY(new SiblingConstraint(10f * scalingFactor))
                    .setTextScale(new PixelConstraint(1.3f * scalingFactor))
                    .setChildOf(tutorialContainer);
                UIComponent section3b = new UIText("• Separate each spell with a comma. Do NOT put your entire cycle in one spell!", false)
                    .setX(new PixelConstraint(60f * scalingFactor))
                    .setY(new SiblingConstraint(6f * scalingFactor))
                    .setTextScale(new PixelConstraint(1.3f * scalingFactor))
                    .setChildOf(tutorialContainer);
                UIComponent section3c = new UIText("  Example (Correct):   R,R,R   |   L,L,L   |   R,L,R", false)
                    .setX(new PixelConstraint(80f * scalingFactor))
                    .setY(new SiblingConstraint(8f * scalingFactor))
                    .setTextScale(new PixelConstraint(1.2f * scalingFactor))
                    .setChildOf(tutorialContainer);
                UIComponent section3d = new UIText("  Example (Incorrect):   R,R,R,L,L,L,R,L,R (all in one)", false)
                    .setX(new PixelConstraint(80f * scalingFactor))
                    .setY(new SiblingConstraint(4f * scalingFactor))
                    .setTextScale(new PixelConstraint(1.2f * scalingFactor))
                    .setChildOf(tutorialContainer);
                UIComponent section3e = new UIText("• Each spell can use R (right), L (left), or . (powder melee).", false)
                    .setX(new PixelConstraint(60f * scalingFactor))
                    .setY(new SiblingConstraint(8f * scalingFactor))
                    .setTextScale(new PixelConstraint(1.3f * scalingFactor))
                    .setChildOf(tutorialContainer);
                // Section 3f: Use MultiLineTextComponent for '.' explanation
                MultiLineTextComponent section3f = new MultiLineTextComponent(
                    "• '.' performs a powder melee: it will melee if you are off cooldown and skip to the next spell if not, leading to a faster spellcycle than having a bunch of L. If your powder special is ready, it will sneak for you.",
                    600f * scalingFactor,
                    1.2f * scalingFactor,
                    new Color(255, 255, 255, 255),
                    tutorialContainer,
                    true
                );
                section3f.setX(new PixelConstraint(80f * scalingFactor));
                section3f.setY(new SiblingConstraint(4f * scalingFactor));

                // Section 4: Selecting Your Cycle
                UIComponent section4 = new UIText("4. Selecting Your Cycle", false)
                    .setX(new PixelConstraint(40f * scalingFactor))
                    .setY(new SiblingConstraint(18f * scalingFactor))
                    .setTextScale(new PixelConstraint(1.7f * scalingFactor))
                    .setChildOf(tutorialContainer);
                UIComponent section4a = new UIText("• After creating a cycle, run /wynnutils spellcycle and choose your cycle in that menu to select it.", false)
                    .setX(new PixelConstraint(60f * scalingFactor))
                    .setY(new SiblingConstraint(10f * scalingFactor))
                    .setTextScale(new PixelConstraint(1.3f * scalingFactor))
                    .setChildOf(tutorialContainer);

                // Section 5: Importing from ChatTriggers (use MultiLineTextComponent)
                UIComponent section5 = new UIText("5. Importing from ChatTriggers", false)
                    .setX(new PixelConstraint(40f * scalingFactor))
                    .setY(new SiblingConstraint(18f * scalingFactor))
                    .setTextScale(new PixelConstraint(1.7f * scalingFactor))
                    .setChildOf(tutorialContainer);
                MultiLineTextComponent section5a = new MultiLineTextComponent(
                    "• You can import your spells from the ChatTriggers version! Look for the import button in the Spellcycle GUI if you haven't added any cycles yet. This will copy your cycles from ChatTriggers if you have them.",
                    600f * scalingFactor,
                    1.3f * scalingFactor,
                    new Color(255, 255, 255, 255),
                    tutorialContainer,
                    true
                );
                section5a.setX(new PixelConstraint(60f * scalingFactor));
                section5a.setY(new SiblingConstraint(10f * scalingFactor));

                appear(tutorialContainer, 0.2f);
                return;
        }
        AtomicReference<CycleConfig> cycleConfig = new AtomicReference<>(getCycleConfig(selectedSpell));
        UIComponent inputFields = new UIContainer()
                .setX(new CenterConstraint())
                .setY(new PixelConstraint(20f * scalingFactor))
                .setWidth(new PixelConstraint(760f * scalingFactor))
                .setHeight(new PixelConstraint(50f * scalingFactor))
                .setChildOf(content);
        UIComponent CPS = new UIContainer()
                .setX(new PixelConstraint(20f * scalingFactor))
                .setY(new PixelConstraint(5f * scalingFactor))
                .setWidth(new PixelConstraint(300f * scalingFactor))
                .setHeight(new PixelConstraint(40f * scalingFactor))
                .setChildOf(inputFields);
        UIComponent Label = new UIText("CPS:", false)
                .setX(new PixelConstraint(3f * scalingFactor))
                .setY(new CenterConstraint())
                .setWidth(new ScaledTextConstraint(1.3f * scalingFactor))
                .setHeight(new ScaledTextConstraint(1.3f * scalingFactor))
                .setChildOf(CPS);
        UIComponent inputBackground = new RoundedRectangleWithBorder(5f, CPS, new SiblingConstraint(20f), new CenterConstraint(), 100f * scalingFactor, 20f * scalingFactor, 1f, new Color(28, 30, 44, 230), new Color(28, 30, 44, 230));
        NumberInput NumberInput = (NumberInput) new NumberInput("Enter CPS:", false)
                .setX(new CenterConstraint())
                .setY(new CenterConstraint())
                .setWidth(new PixelConstraint(90f * scalingFactor))
                .setHeight(new PixelConstraint(15f * scalingFactor))
                .setTextScale(new PixelConstraint(2f))
                .setChildOf(inputBackground);
        NumberInput.setText(String.valueOf(cycleConfig.get().cps()));
        inputBackground.onMouseClick((component, event) -> {
            NumberInput.grabWindowFocus();
            inputBackground.setColor(new Color(150, 100, 255, 230));
            return null;
        });
        NumberInput.onFocusLost((component) -> {
            inputBackground.setColor(new Color(28, 30, 44, 230));
            return null;
        });
        NumberInput.onKeyType((u, c, i) -> {
            editCycle(selectedSpell, new CycleConfig(cycleConfig.get().cycle(), Integer.parseInt(NumberInput.getText()), 0));
            cycleConfig.set(getCycleConfig(selectedSpell));
            return null;
        });
        UIComponent cycleContainer = new UIContainer()
                .setX(new CenterConstraint())
                .setY(new SiblingConstraint(20f * scalingFactor))
                .setWidth(new PixelConstraint(760f * scalingFactor))
                .setHeight(new PixelConstraint(550f * scalingFactor))
                .setChildOf(content);
        UIImage Add = (UIImage) UIImage.ofResource("/assets/wynnutils/plus.png")
                .setX(new PixelConstraint(20f * scalingFactor))
                .setY(new SiblingConstraint(10f * scalingFactor))
                .setHeight(new PixelConstraint(20f * scalingFactor))
                .setWidth(new PixelConstraint(20f * scalingFactor))
                .setChildOf(cycleContainer);
        Add.onMouseEnterRunnable(() -> {
            Add.animateTo(Add.makeAnimation().setColorAnimation(Animations.IN_OUT_EXP, 0.2f, new ConstantColorConstraint(new Color(18, 255, 0, 255))));
        });
        Add.onMouseLeaveRunnable(() -> {
            Add.animateTo(Add.makeAnimation().setColorAnimation(Animations.IN_OUT_EXP, 0.2f, new ConstantColorConstraint(new Color(255, 255, 255, 255))));
        });
        UIComponent CycleContainer = new UIRoundedRectangle(5f)
                .setX(new CenterConstraint())
                .setY(new SiblingConstraint(10f * scalingFactor))
                .setWidth(new PixelConstraint(720f * scalingFactor))
                .setHeight(new PixelConstraint(500f * scalingFactor))
                .setColor(new Color(255,255,255,5))
                .setChildOf(cycleContainer);
        UIComponent ScrollContainer = new ScrollComponent("No Spells yet")
                .setX(new CenterConstraint())
                .setY(new PixelConstraint(10f * scalingFactor))
                .setWidth(new PixelConstraint(720f * scalingFactor))
                .setHeight(new PixelConstraint(480f * scalingFactor))
                .setChildOf(CycleContainer);
        createSpells(ScrollContainer);
        Add.onMouseClick((component, event) -> {
            CycleConfig currentConfig = getCycleConfig(selectedSpell);
            String[][] oldSpells = currentConfig.cycle();
            String[][] newSpells = new String[oldSpells.length + 1][];
            System.arraycopy(oldSpells, 0, newSpells, 0, oldSpells.length);
            newSpells[oldSpells.length] = new String[]{"R", "R", "R"}; // Default new spell
            editCycle(selectedSpell, new CycleConfig(newSpells, currentConfig.cps(), currentConfig.cooldown()));
            createSpells(ScrollContainer);
            return null;
        });
        appear(inputFields, 0.2f);
        appear(Add, 0.3f);
        appear(CycleContainer, 0.3f);
    }
    private void createSpells(UIComponent parent) {
        parent.clearChildren();
        AtomicReference<CycleConfig> cycleConfig = new AtomicReference<>(getCycleConfig(selectedSpell));
        String[][] spells = cycleConfig.get().cycle();
        for (int i = 0; i < spells.length; i++) {
            final int index = i;
            String spellString = String.join(",", spells[i]);
            UIComponent Background = new UIRoundedRectangle(5f)
                    .setX(new CenterConstraint())
                    .setY(new SiblingConstraint(8f * scalingFactor))
                    .setWidth(new PixelConstraint(700f * scalingFactor))
                    .setHeight(new PixelConstraint(40f * scalingFactor))
                    .setColor(new Color(255, 255, 255, 3))
                    .setChildOf(parent);
            UIText label = (UIText) new UIText(spellString, false)
                    .setX(new CenterConstraint())
                    .setY(new CenterConstraint())
                    .setWidth(new ScaledTextConstraint(1.6f * scalingFactor))
                    .setHeight(new ScaledTextConstraint(1.6f * scalingFactor))
                    .setChildOf(Background);
            Background.onMouseEnterRunnable(() -> {
                Background.animateTo(Background.makeAnimation().setHeightAnimation(Animations.OUT_EXP, 0.5f, new PixelConstraint(45f * scalingFactor)));
                Background.animateTo(Background.makeAnimation().setColorAnimation(Animations.OUT_EXP, 0.5f, new ConstantColorConstraint(new Color(255, 255, 255, 10))));
            });
            Background.onMouseLeaveRunnable(() -> {
                Background.animateTo(Background.makeAnimation().setHeightAnimation(Animations.OUT_EXP, 0.5f, new PixelConstraint(40f * scalingFactor)));
                Background.animateTo(Background.makeAnimation().setColorAnimation(Animations.OUT_EXP, 0.5f, new ConstantColorConstraint(new Color(255, 255, 255, 3))));
            });
            Background.onMouseClick((component, event) -> {
                if (event.getMouseButton() == 1) {
                    UIComponent roundedRectangle = new UIRoundedRectangle(5f)
                            .setX(new PixelConstraint(event.getAbsoluteX()))
                            .setY(new PixelConstraint(event.getAbsoluteY()))
                            .setWidth(new PixelConstraint(100f * scalingFactor))
                            .setHeight(new PixelConstraint(75f * scalingFactor))
                            .setColor(new Color(15, 17, 31, 255))
                            .setChildOf(getWindow());
                    roundedRectangle.onMouseLeaveRunnable(roundedRectangle::hide);
                    UIComponent renameButton = new UIRoundedRectangle(5f)
                            .setX(new CenterConstraint())
                            .setY(new PixelConstraint(4f * scalingFactor))
                            .setWidth(new PixelConstraint(90f * scalingFactor))
                            .setHeight(new PixelConstraint(30f * scalingFactor))
                            .setColor(new Color(0, 0, 0, 0))
                            .setChildOf(roundedRectangle);
                    UIComponent renameIcon = UIImage.ofResource("/assets/wynnutils/edit.png")
                            .setX(new PixelConstraint(3f * scalingFactor))
                            .setY(new CenterConstraint())
                            .setWidth(new PixelConstraint(18f * scalingFactor))
                            .setHeight(new PixelConstraint(18f * scalingFactor))
                            .setChildOf(renameButton);
                    UIComponent rename = new UIText("Edit")
                            .setX(new SiblingConstraint(10f * scalingFactor))
                            .setY(new CenterConstraint())
                            .setWidth(new ScaledTextConstraint(1.3f * scalingFactor))
                            .setHeight(new ScaledTextConstraint(1.3f * scalingFactor))
                            .setChildOf(renameButton);
                    UIComponent deleteButton = new UIRoundedRectangle(5f)
                            .setX(new CenterConstraint())
                            .setY(new SiblingConstraint(6f * scalingFactor))
                            .setWidth(new PixelConstraint(90f * scalingFactor))
                            .setHeight(new PixelConstraint(30f * scalingFactor))
                            .setColor(new Color(0, 0, 0, 0))
                            .setChildOf(roundedRectangle);
                    UIComponent deleteIcon = UIImage.ofResource("/assets/wynnutils/trash.png")
                            .setX(new PixelConstraint(3f * scalingFactor))
                            .setY(new CenterConstraint())
                            .setWidth(new PixelConstraint(18f * scalingFactor))
                            .setHeight(new PixelConstraint(18f * scalingFactor))
                            .setChildOf(deleteButton);
                    UIComponent delete = new UIText("Delete")
                            .setX(new SiblingConstraint(10f * scalingFactor))
                            .setY(new CenterConstraint())
                            .setWidth(new ScaledTextConstraint(1.3f * scalingFactor))
                            .setHeight(new ScaledTextConstraint(1.3f * scalingFactor))
                            .setChildOf(deleteButton);
                    renameButton.onMouseEnterRunnable(() -> {renameButton.animateTo(renameButton.makeAnimation().setColorAnimation(Animations.OUT_EXP, 0.5f, new ConstantColorConstraint(new Color(30, 32, 46, 230))));});
                    renameButton.onMouseLeaveRunnable(() -> {renameButton.animateTo(renameButton.makeAnimation().setColorAnimation(Animations.OUT_EXP, 0.5f, new ConstantColorConstraint(new Color(30, 32, 46, 0))));});
                    deleteButton.onMouseEnterRunnable(() -> {deleteButton.animateTo(deleteButton.makeAnimation().setColorAnimation(Animations.OUT_EXP, 0.5f, new ConstantColorConstraint(new Color(100, 32, 46, 230))));});
                    deleteButton.onMouseLeaveRunnable(() -> {deleteButton.animateTo(deleteButton.makeAnimation().setColorAnimation(Animations.OUT_EXP, 0.5f, new ConstantColorConstraint(new Color(30, 32, 46, 0))));});
                    renameButton.onMouseClick((component1, event1) -> {
                        getWindow().removeChild(roundedRectangle);
                        String currentText = label.getText();
                        Background.clearChildren();
                        SpellInput TextInput = (SpellInput) new SpellInput("Enter Spell (R/L/.,R/L/.,R/L/.)")
                                .setX(new CenterConstraint())
                                .setY(new CenterConstraint())
                                .setWidth(new PixelConstraint(60f * scalingFactor))
                                .setHeight(new PixelConstraint(18f * scalingFactor))
                                .setTextScale(new PixelConstraint(2f))
                                .setChildOf(Background);
                        TextInput.setText(currentText);
                        TextInput.grabWindowFocus();
                        TextInput.onFocusLost((component2) -> {
                            String[] spellArray = TextInput.getText().split(",");
                            String[][] newSpells = cycleConfig.get().cycle().clone();
                            newSpells[index] = spellArray;
                            editCycle(selectedSpell, new CycleConfig(newSpells, cycleConfig.get().cps(), cycleConfig.get().cooldown()));
                            createSpells(parent);
                            return null;
                        });
                        return null;
                    });
                    deleteButton.onMouseClick((component1, event1) -> {
                        getWindow().removeChild(roundedRectangle);
                        String[][] oldSpells = cycleConfig.get().cycle();
                        List<String[]> spellList = new ArrayList<>(java.util.Arrays.asList(oldSpells));
                        spellList.remove(index);
                        String[][] newSpells = spellList.toArray(new String[0][]);
                        editCycle(selectedSpell, new CycleConfig(newSpells, cycleConfig.get().cps(), cycleConfig.get().cooldown()));
                        createSpells(parent);
                        return null;
                    });
                    return null;
                }
                return null;
            });
        }
    }
    private class SidebarItem extends UIRoundedRectangle {
        private UIText label;
        private boolean selected = false;
        private static List<EditorGUI.SidebarItem> allItems = new ArrayList<>();
        private float scalingFactor = GUIUtils.getScale();

        public SidebarItem(float radius, String text, UIComponent parent) {
            super(radius);
            allItems.add(this);
            this.setX(new CenterConstraint());
            this.setY(new SiblingConstraint(8f * scalingFactor));
            this.setWidth(new PixelConstraint(180f * scalingFactor));
            this.setHeight(new PixelConstraint(40f * scalingFactor));
            this.setColor(new Color(255, 255, 255, 3));
            this.setChildOf(parent);
            this.onMouseEnterRunnable(() -> {
                this.animateTo(this.makeAnimation().setHeightAnimation(Animations.OUT_EXP, 0.5f, new PixelConstraint(45f * scalingFactor)));
                if (selected) return;
                this.animateTo(this.makeAnimation().setColorAnimation(Animations.OUT_EXP, 0.5f, new ConstantColorConstraint(new Color(255, 255, 255, 10))));
            });
            this.onMouseLeaveRunnable(() -> {
                this.animateTo(this.makeAnimation().setHeightAnimation(Animations.OUT_EXP, 0.5f, new PixelConstraint(40f * scalingFactor)));
                if (selected) return;
                this.animateTo(this.makeAnimation().setColorAnimation(Animations.OUT_EXP, 0.5f, new ConstantColorConstraint(new Color(255, 255, 255, 3))));
            });
            appear(this, 0.3f);
            if (selectedSpell.equals(text)) {
                select();
            }
            this.onMouseClick((component, event) -> {
                if (event.getMouseButton() == 1) {
                    UIComponent roundedRectangle = new UIRoundedRectangle(5f)
                            .setX(new PixelConstraint(event.getAbsoluteX()))
                            .setY(new PixelConstraint(event.getAbsoluteY()))
                            .setWidth(new PixelConstraint(100f * scalingFactor))
                            .setHeight(new PixelConstraint(75f * scalingFactor))
                            .setColor(new Color(15, 17, 31, 255))
                            .setChildOf(getWindow());
                    roundedRectangle.onMouseLeaveRunnable(roundedRectangle::hide);
                    UIComponent renameButton = new UIRoundedRectangle(5f)
                            .setX(new CenterConstraint())
                            .setY(new PixelConstraint(4f * scalingFactor))
                            .setWidth(new PixelConstraint(90f * scalingFactor))
                            .setHeight(new PixelConstraint(30f * scalingFactor))
                            .setColor(new Color(0, 0, 0, 0))
                            .setChildOf(roundedRectangle);
                    UIComponent renameIcon = UIImage.ofResource("/assets/wynnutils/edit.png")
                            .setX(new PixelConstraint(3f * scalingFactor))
                            .setY(new CenterConstraint())
                            .setWidth(new PixelConstraint(18f * scalingFactor))
                            .setHeight(new PixelConstraint(18f * scalingFactor))
                            .setChildOf(renameButton);
                    UIComponent rename = new UIText("Rename")
                            .setX(new SiblingConstraint(10f * scalingFactor))
                            .setY(new CenterConstraint())
                            .setWidth(new ScaledTextConstraint(1.3f * scalingFactor))
                            .setHeight(new ScaledTextConstraint(1.3f * scalingFactor))
                            .setChildOf(renameButton);
                    UIComponent deleteButton = new UIRoundedRectangle(5f)
                            .setX(new CenterConstraint())
                            .setY(new SiblingConstraint(6f * scalingFactor))
                            .setWidth(new PixelConstraint(90f * scalingFactor))
                            .setHeight(new PixelConstraint(30f * scalingFactor))
                            .setColor(new Color(0, 0, 0, 0))
                            .setChildOf(roundedRectangle);
                    UIComponent deleteIcon = UIImage.ofResource("/assets/wynnutils/trash.png")
                            .setX(new PixelConstraint(3f * scalingFactor))
                            .setY(new CenterConstraint())
                            .setWidth(new PixelConstraint(18f * scalingFactor))
                            .setHeight(new PixelConstraint(18f * scalingFactor))
                            .setChildOf(deleteButton);
                    UIComponent delete = new UIText("Delete")
                            .setX(new SiblingConstraint(10f * scalingFactor))
                            .setY(new CenterConstraint())
                            .setWidth(new ScaledTextConstraint(1.3f * scalingFactor))
                            .setHeight(new ScaledTextConstraint(1.3f * scalingFactor))
                            .setChildOf(deleteButton);
                    renameButton.onMouseEnterRunnable(() -> {renameButton.animateTo(renameButton.makeAnimation().setColorAnimation(Animations.OUT_EXP, 0.5f, new ConstantColorConstraint(new Color(30, 32, 46, 230))));});
                    renameButton.onMouseLeaveRunnable(() -> {renameButton.animateTo(renameButton.makeAnimation().setColorAnimation(Animations.OUT_EXP, 0.5f, new ConstantColorConstraint(new Color(30, 32, 46, 0))));});
                    deleteButton.onMouseEnterRunnable(() -> {deleteButton.animateTo(deleteButton.makeAnimation().setColorAnimation(Animations.OUT_EXP, 0.5f, new ConstantColorConstraint(new Color(100, 32, 46, 230))));});
                    deleteButton.onMouseLeaveRunnable(() -> {deleteButton.animateTo(deleteButton.makeAnimation().setColorAnimation(Animations.OUT_EXP, 0.5f, new ConstantColorConstraint(new Color(30, 32, 46, 0))));});
                    renameButton.onMouseClick((component1, event1) -> {
                        getWindow().removeChild(roundedRectangle);
                        String currentText = label.getText();
                        this.clearChildren();
                        UITextInput TextInput = (UITextInput) new UITextInput("Enter Cycle Name")
                                .setX(new CenterConstraint())
                                .setY(new CenterConstraint())
                                .setWidth(new PixelConstraint(150f * scalingFactor))
                                .setHeight(new PixelConstraint(20f * scalingFactor))
                                .setTextScale(new PixelConstraint(2f))
                                .setChildOf(this);
                        TextInput.setText(currentText);
                        TextInput.grabWindowFocus();
                        UIText BottomText = (UIText) new UIText("Enter Cycle Name")
                                .setX(new PixelConstraint(20f * scalingFactor))
                                .setY(new SiblingConstraint(2f * scalingFactor))
                                .setWidth(new ScaledTextConstraint(0.8f * scalingFactor))
                                .setHeight(new ScaledTextConstraint(0.8f * scalingFactor))
                                .setChildOf(this);

                        TextInput.onKeyType((component3, character, integer) -> {
                            String currentText2 = TextInput.getText();
                            boolean nameExists = java.util.Arrays.asList(Spellmacro.getCycleNames()).contains(currentText2);
                            if (currentText2.isEmpty()) {
                                BottomText.setText("§cName cannot be empty.");
                            } else if (nameExists && !currentText2.equals(currentText)) {
                                BottomText.setText("§cCycle with this name already exists.");
                            } else {
                                BottomText.setText("§aName is valid.");
                            }
                            return null;
                        });
                        TextInput.onFocusLost((component2) -> {
                            String currentText3 = TextInput.getText();
                            Spellmacro.renameCycle(currentText, currentText3);
                            this.clearChildren();
                            if (selected) {
                                selectedSpell = currentText3;
                            }
                            label.setText(currentText3);
                            label.setChildOf(this);
                            return null;
                        });
                        return null;
                    });
                    deleteButton.onMouseClick((component1, event1) -> {
                        getWindow().removeChild(roundedRectangle);
                        if (selected) {
                            selectedSpell = "";
                            updateContent();
                        }
                        Spellmacro.deleteCycle(label.getText());
                        parent.removeChild(this);
                        return null;
                    });
                    return null;
                } else {
                    if (selectedSpell.equals(label.getText())) {
                        return null;
                    } else {
                        select();
                        deselectAllExcept(this);
                        selectedSpell = label.getText();
                        updateContent();
                    }
                    return null;
                }
            });
            label = (UIText) new UIText(text, false)
                    .setX(new CenterConstraint())
                    .setY(new CenterConstraint())
                    .setWidth(new ScaledTextConstraint(1.6f * scalingFactor))
                    .setHeight(new ScaledTextConstraint(1.6f * scalingFactor))
                    .setChildOf(this);
        }

        public void select() {
            selected = true;
            this.animateTo(this.makeAnimation().setColorAnimation(Animations.OUT_EXP, 0.5f, new ConstantColorConstraint(new Color(150, 100, 255, 200))));
        }

        public void deselect() {
            selected = false;
            this.animateTo(this.makeAnimation().setColorAnimation(Animations.OUT_EXP, 0.5f, new ConstantColorConstraint(new Color(255, 255, 255, 3))));
        }

        private static void deselectAllExcept(SidebarItem exceptItem) {
            for (SidebarItem item : allItems) {
                if (item != exceptItem) {
                    item.deselect();
                }
            }
        }
    }
}
