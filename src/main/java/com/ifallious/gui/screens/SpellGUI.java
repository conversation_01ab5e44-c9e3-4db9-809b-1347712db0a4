package com.ifallious.gui.screens;

import com.ifallious.features.spells.Spellmacro;
import com.ifallious.gui.components.SpellcycleButtonComponent;
import com.ifallious.gui.components.RoundedRectangleWithBorder;
import com.ifallious.utils.ErrorReporter;
import gg.essential.elementa.ElementaVersion;
import gg.essential.elementa.UIComponent;
import gg.essential.elementa.WindowScreen;
import gg.essential.elementa.components.*;
import gg.essential.elementa.constraints.*;
import gg.essential.elementa.constraints.animation.Animations;
import gg.essential.universal.UChat;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.awt.*;
import java.util.concurrent.ConcurrentLinkedQueue;

import static com.ifallious.gui.GUIUtils.getScale;

public class SpellGUI extends WindowScreen {
    public float scalingFactor = getScale();
    private final float maincontainerpadding = 6 * scalingFactor;
    private final float borderwidth = 2;
    public static UIComponent window;
    private final ConcurrentLinkedQueue<Runnable> pendingUpdates = new ConcurrentLinkedQueue<>();

    UIComponent background = new RoundedRectangleWithBorder(10f, getWindow(), new CenterConstraint(), new CenterConstraint(), 400f * scalingFactor, 500f * scalingFactor, borderwidth, new Color(150, 100, 255, 230), new Color(20, 22, 36, 230));
    UIComponent container = new UIContainer()
            .setX(new CenterConstraint())
            .setY(new CenterConstraint())
            .setWidth(new PixelConstraint(400f * scalingFactor))
            .setHeight(new PixelConstraint(500f * scalingFactor))
            .setChildOf(background);
    ScrollComponent content = (ScrollComponent) new ScrollComponent("No Cycles", 0f)
            .setX(new CenterConstraint())
            .setY(new PixelConstraint(20f * scalingFactor))
            .setWidth(new PixelConstraint(400f * scalingFactor))
            .setHeight(new PixelConstraint(400f * scalingFactor))
            .setChildOf(container);
    UIComponent footer = new UIRoundedRectangle(10f)
            .setX(new CenterConstraint())
            .setY(new SiblingConstraint(0f))
            .setWidth(new PixelConstraint(400f * scalingFactor))
            .setHeight(new PixelConstraint(80f * scalingFactor))
            .setColor(new Color(30, 32, 50, 230))
            .setChildOf(container);

    public SpellGUI() {
        super(ElementaVersion.V8, true, false, false, 1);
        try {
            window = getWindow();
            setUpContent();
        } catch (Exception e) {
            ErrorReporter.reportError("SpellGUI initialization failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
        }
    }

    private void setUpContent() {
        content.clearChildren();
        String currentCycle = Spellmacro.getCurrentCycle();

        if (Spellmacro.getCycleNames().length == 0) {
            UIComponent importButton = new UIContainer()
                    .setX(new CenterConstraint())
                    .setY(new CenterConstraint())
                    .setWidth(new PixelConstraint(200f * scalingFactor))
                    .setHeight(new PixelConstraint(50f * scalingFactor))
                    .setChildOf(content);

            UIComponent button = new UIRoundedRectangle(5f)
                    .setX(new CenterConstraint())
                    .setY(new CenterConstraint())
                    .setWidth(new PixelConstraint(200f * scalingFactor))
                    .setHeight(new PixelConstraint(50f * scalingFactor))
                    .setColor(new Color(255, 255, 255, 10))
                    .setChildOf(importButton);
            button.onMouseEnterRunnable(() -> {
                button.animateTo(button.makeAnimation().setColorAnimation(Animations.OUT_EXP, 0.5f, new ConstantColorConstraint(new Color(255, 255, 255, 30))));
            });
            button.onMouseLeaveRunnable(() -> {
                button.animateTo(button.makeAnimation().setColorAnimation(Animations.OUT_EXP, 0.5f, new ConstantColorConstraint(new Color(255, 255, 255, 10))));
            });

            new UIText("Import from ChatTriggers", false)
                    .setX(new CenterConstraint())
                    .setY(new CenterConstraint())
                    .setWidth(new ScaledTextConstraint(1.3f * scalingFactor))
                    .setHeight(new ScaledTextConstraint(1.3f * scalingFactor))
                    .setColor(Color.WHITE)
                    .setChildOf(importButton);

            importButton.onMouseClickConsumer(event -> {
                String result = Spellmacro.importCyclesFromChatTriggers();
                UChat.chat(result);
                setUpContent();
            });
        } else {
            int id = 1;

            // Process cycles in pairs
            for (int i = 0; i < Spellmacro.getCycleNames().length; i += 2) {
                // Create a row container
                UIComponent rowContainer = new UIContainer()
                        .setX(new CenterConstraint())
                        .setY(new SiblingConstraint(10f * scalingFactor))
                        .setWidth(new PixelConstraint(380f * scalingFactor))
                        .setHeight(new PixelConstraint(60f * scalingFactor))
                        .setChildOf(content);

                // Add first button in the row
                String cycle1 = Spellmacro.getCycleNames()[i];
                String finalCycle1 = cycle1.equals(currentCycle) ? "§l§6✦ §r§f" + cycle1 + " §l§6✦" : cycle1;
                Color color1 = cycle1.equals(currentCycle) ? new Color(116, 72, 194, 230) : new Color(45, 47, 70, 230);
                Spellmacro.CycleConfig config1 = Spellmacro.getCycleConfig(cycle1);
                new SpellcycleButtonComponent(rowContainer, new PixelConstraint(3f * scalingFactor), new CenterConstraint(), 180f * scalingFactor, 50f * scalingFactor, color1, new Color(130, 87, 229, 230), finalCycle1, config1.cps(), 1.5f * scalingFactor, () -> {
                    Spellmacro.setCycle(cycle1);
                    setUpContent();
                    this.close();
                }, this);

                // Add second button in the row if it exists
                if (i + 1 < Spellmacro.getCycleNames().length) {
                    String cycle2 = Spellmacro.getCycleNames()[i + 1];
                    String finalCycle2 = cycle2.equals(currentCycle) ? "§l§6✦ §r§f" + cycle2 + " §l§6✦" : cycle2;
                    Color color2 = cycle2.equals(currentCycle) ? new Color(116, 72, 194, 230) : new Color(45, 47, 70, 230);
                    Spellmacro.CycleConfig config2 = Spellmacro.getCycleConfig(cycle2);
                    new SpellcycleButtonComponent(rowContainer, new SiblingConstraint(10f * scalingFactor), new CenterConstraint(), 180f * scalingFactor, 50f * scalingFactor, color2, new Color(130, 87, 229, 230), finalCycle2, config2.cps(), 1.5f * scalingFactor, () -> {
                        Spellmacro.setCycle(cycle2);
                        setUpContent();
                        this.close();
                    }, this);
                }
            }
        }
    }
    @Override
    public boolean shouldPause() {
        return false;
    }
}
