package com.ifallious.gui.screens;

import com.ifallious.gui.components.*;
import com.ifallious.gui.subscreens.*;
import com.ifallious.utils.ErrorReporter;
import gg.essential.elementa.UIComponent;
import gg.essential.elementa.WindowScreen;
import gg.essential.elementa.ElementaVersion;
import gg.essential.elementa.components.*;
import gg.essential.elementa.constraints.*;
import gg.essential.elementa.constraints.animation.Animations;
import gg.essential.elementa.effects.ScissorEffect;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.awt.*;

import static com.ifallious.gui.GUIUtils.getScale;

public class ConfigGUI extends WindowScreen {
    public float scalingFactor = getScale();
    private final float maincontainerpadding = 6 * scalingFactor;
    private final float borderwidth = 2;
    public static String selectedMenu = "Info";

    UIComponent background = new RoundedRectangleWithBorder(10f, getWindow(), new CenterConstraint(), new CenterConstraint(), 1000f * scalingFactor + maincontainerpadding * 2, 700f * scalingFactor + maincontainerpadding * 2, borderwidth, new Color(150, 100, 255, 230), new Color(20, 22, 36, 230));
    UIComponent container = new UIContainer()
            .setX(new CenterConstraint())
            .setY(new CenterConstraint())
            .setWidth(new PixelConstraint(1000f * scalingFactor))
            .setHeight(new PixelConstraint(700f * scalingFactor))
            .setChildOf(background);
    UIComponent sidebar = new UIRoundedRectangle(10f)
            .setX(new SiblingConstraint(20f * scalingFactor))
            .setY(new PixelConstraint(0f))
            .setWidth(new PixelConstraint(200f * scalingFactor))
            .setHeight(new PixelConstraint(700f * scalingFactor))
            .enableEffect(new ScissorEffect())
            .setColor(new Color(20, 22, 36, 230))
            .setChildOf(container);
    UIComponent content = new UIRoundedRectangle(10f)
            .setX(new SiblingConstraint(20f * scalingFactor))
            .setY(new PixelConstraint(0f))
            .setWidth(new PixelConstraint(780f * scalingFactor))
            .setHeight(new PixelConstraint(700f * scalingFactor))
            .enableEffect(new ScissorEffect())
            .setColor(new Color(20, 22, 36, 230))
            .setChildOf(container);

    public ConfigGUI() {
        super(ElementaVersion.V8, true, false, true, 1);
        try {
            createSidebar();
            updateContent(true);
        } catch (Exception e) {
            ErrorReporter.reportError("ConfigGUI initialization failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
        }
    }

    private void createSidebar() {
        UIComponent header = new UIContainer()
                .setX(new CenterConstraint())
                .setY(new PixelConstraint(0f))
                .setWidth(new PixelConstraint(180f * scalingFactor))
                .setHeight(new PixelConstraint(40f * scalingFactor))
                .setChildOf(sidebar);
        UIComponent collapseButton = UIImage.ofResource("/assets/wynnutils/layout-sidebar-left-collapse.png")
                .setX(new SiblingConstraint(20f * scalingFactor))
                .setY(new CenterConstraint())
                .setWidth(new PixelConstraint(20f * scalingFactor))
                .setHeight(new PixelConstraint(20f * scalingFactor))
                .setChildOf(header);
        UIComponent title = new UIText("Wynnutils", false)
                .setX(new CenterConstraint())
                .setY(new CenterConstraint())
                .setTextScale(new PixelConstraint(2f * scalingFactor))
                .setChildOf(header);
        UIComponent expandButton = UIImage.ofResource("/assets/wynnutils/layout-sidebar-left-expand.png")
                .setX(new CenterConstraint())
                .setY(new CenterConstraint())
                .setWidth(new PixelConstraint(20f * scalingFactor))
                .setHeight(new PixelConstraint(20f * scalingFactor))
                .setTextScale(new PixelConstraint(0f))
                .setChildOf(header);
        expandButton.hide();
        UIComponent line = new UIRoundedRectangle(5f)
                .setX(new CenterConstraint())
                .setY(new SiblingConstraint(4f * scalingFactor))
                .setWidth(new PixelConstraint(140f * scalingFactor))
                .setHeight(new PixelConstraint(2f * scalingFactor))
                .setColor(new Color(150, 100, 255, 200))
                .setChildOf(sidebar);
        collapseButton.onMouseClick((component, event) -> {
            header.removeChild(collapseButton);
            title.animateTo(title.makeAnimation().setColorAnimation(Animations.IN_OUT_EXP, 0.2f, new ConstantColorConstraint(new Color(255, 255, 255, 0))).onCompleteRunnable(() -> {header.removeChild(title); expandButton.unhide(true);}));
            SideBarItem.collapseAll();
            int timerid =content.startTimer(30, 0, (integer) -> {
                updateContent(false);
                return null;
            });
            content.delay(500, () -> {
                content.stopTimer(timerid);
                return null;
            });
            content.animateTo(content.makeAnimation().setWidthAnimation(Animations.IN_OUT_EXP, 0.5f, new PixelConstraint(950f * scalingFactor)).setXAnimation(Animations.IN_OUT_EXP, 0.5f, new SiblingConstraint(10f * scalingFactor)));
            sidebar.animateTo(sidebar.makeAnimation().setWidthAnimation(Animations.IN_OUT_EXP, 0.5f, new PixelConstraint(40f * scalingFactor)));
            line.animateTo(line.makeAnimation().setWidthAnimation(Animations.IN_OUT_EXP, 0.5f, new PixelConstraint(20f * scalingFactor)));
            return null;
        });
        expandButton.onMouseClick((component, event) -> {
            expandButton.hide();
            header.addChildren(collapseButton, title);
            SideBarItem.expandAll();
            int timerid =content.startTimer(30, 0, (integer) -> {
                updateContent(false);
                return null;
            });
            content.delay(500, () -> {
                content.stopTimer(timerid);
                return null;
            });
            content.animateTo(content.makeAnimation().setWidthAnimation(Animations.IN_OUT_EXP, 0.5f, new PixelConstraint(780f * scalingFactor)).setXAnimation(Animations.IN_OUT_EXP, 0.5f, new SiblingConstraint(20f * scalingFactor)));
            sidebar.animateTo(sidebar.makeAnimation().setWidthAnimation(Animations.IN_OUT_EXP, 0.5f, new PixelConstraint(200f * scalingFactor)));
            title.animateTo(title.makeAnimation().setColorAnimation(Animations.IN_OUT_EXP, 0.2f, new ConstantColorConstraint(new Color(255, 255, 255, 255))));
            line.animateTo(line.makeAnimation().setWidthAnimation(Animations.IN_OUT_EXP, 0.5f, new PixelConstraint(140f * scalingFactor)));
            return null;
        });
        UIComponent sidebarItems = new UIContainer()
                .setX(new PixelConstraint(4f * scalingFactor))
                .setY(new SiblingConstraint(8f * scalingFactor))
                .setWidth(new PixelConstraint(180f * scalingFactor))
                .setHeight(new PixelConstraint(640f * scalingFactor))
                .setChildOf(sidebar);
        new SideBarItem(5f, "War", "/assets/wynnutils/tower.png", sidebarItems, this);
        new SideBarItem(5f, "SpellMacro", "/assets/wynnutils/wand.png", sidebarItems, this);
        new SideBarItem(5f, "RMT", "/assets/wynnutils/flare.png", sidebarItems, this);
        new SideBarItem(5f, "Raid", "/assets/wynnutils/skull.png", sidebarItems, this);
        new SideBarItem(5f, "Misc.", "/assets/wynnutils/category.png", sidebarItems, this);
        new SideBarItem(5f, "AI", "/assets/wynnutils/ai.png", sidebarItems, this);
    }

    @Override
    public boolean shouldPause() {
        return false;
    }
    public void select(String name) {
        selectedMenu = name;
    }
    public void updateContent(Boolean first) {
        try {
            content.clearChildren();
            switch(selectedMenu) {
                case "Info":
                    new InfoContent(content);
                    break;
                case "War":
                    new WarCategoryScreen(content, first);
                    break;
                case "SpellMacro":
                    new SpellMacroCategoryScreen(content, first);
                    break;
                case "RMT":
                    new RMTCategoryScreen(content, first);
                    break;
                case "Misc.":
                    new MiscCategoryScreen(content, first);
                    break;
                case "Raid":
                    new RaidCategoryScreen(content, first);
                    break;
                case "AI":
                    new AICategoryScreen(content, first);
                    break;
            }
        } catch (Exception e) {
            ErrorReporter.reportError("ConfigGUI updateContent failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
        }
    }
}
