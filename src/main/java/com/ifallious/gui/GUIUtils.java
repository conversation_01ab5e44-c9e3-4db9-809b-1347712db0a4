package com.ifallious.gui;

import gg.essential.elementa.UIComponent;
import gg.essential.elementa.constraints.ConstantColorConstraint;
import gg.essential.elementa.constraints.PixelConstraint;
import gg.essential.elementa.constraints.animation.Animations;
import gg.essential.universal.UChat;
import gg.essential.universal.UResolution;

import java.awt.*;

public class GUIUtils {
    public static void appear(UIComponent component, float duration) {
        Color originalColor = component.getColor();
        Float width = component.getWidth();
        Float height = component.getHeight();
        component.setColor(new Color(originalColor.getRed(), originalColor.getGreen(), originalColor.getBlue(), 0)).setHeight(new PixelConstraint(0f)).setWidth(new PixelConstraint(0f));
        component.animateTo(component.makeAnimation().setColorAnimation(Animations.IN_OUT_EXP, duration, new ConstantColorConstraint(originalColor)).setHeightAnimation(Animations.IN_OUT_EXP, duration * 1.3f, new PixelConstraint(height)).setWidthAnimation(Animations.IN_OUT_EXP, duration * 1.3f, new PixelConstraint(width)));
    }
    public static void fadeIn(UIComponent component, float duration) {
        Color originalColor = component.getColor();
        component.setColor(new Color(originalColor.getRed(), originalColor.getGreen(), originalColor.getBlue(), 0));
        component.animateTo(component.makeAnimation().setColorAnimation(Animations.IN_OUT_EXP, duration, new ConstantColorConstraint(originalColor)));
    }
    public static float getScale() {
        int ScreenHeight = UResolution.getViewportHeight();
        return ScreenHeight / 1080f;
    }
}
