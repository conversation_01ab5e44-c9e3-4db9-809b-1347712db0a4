package com.ifallious.gui;

import gg.essential.elementa.ElementaVersion;
import gg.essential.elementa.UIComponent;
import gg.essential.elementa.WindowScreen;
import gg.essential.elementa.components.UIBlock;
import gg.essential.elementa.constraints.CenterConstraint;
import gg.essential.elementa.constraints.ChildBasedSizeConstraint;
import gg.essential.elementa.constraints.PixelConstraint;
import gg.essential.elementa.constraints.animation.AnimatingConstraints;
import gg.essential.elementa.constraints.animation.Animations;
import gg.essential.elementa.effects.ScissorEffect;
import gg.essential.universal.GuiScale;

import java.awt.*;

public class JavaTestGui extends WindowScreen {
    UIComponent box = new UIBlock()
            .setX(new CenterConstraint())
            .setY(new PixelConstraint(10f))
            .setWidth(new PixelConstraint(10f))
            .setHeight(new PixelConstraint(36f))
            .setColor(Color.BLACK)
            .setChildOf(getWindow())
            .enableEffect(new ScissorEffect());

    public JavaTestGui() {
        super(ElementaVersion.V8, true, true, false, GuiScale.scaleForScreenSize().ordinal());
        box.onMouseEnterRunnable(() -> {
            AnimatingConstraints anim = box.makeAnimation();
            anim.setWidthAnimation(Animations.OUT_EXP, 0.5f, new ChildBasedSizeConstraint(2f));
            anim.onCompleteRunnable(() -> {
            });
            box.animateTo(anim);
        });
    }
}
