package com.ifallious.gui;

import com.ifallious.gui.screens.ConfigGUI;
import com.ifallious.gui.screens.EditorGUI;
import com.ifallious.gui.screens.SpellGUI;
import com.ifallious.utils.minecraft.Tick;
import com.ifallious.utils.ErrorReporter;
import gg.essential.elementa.WindowScreen;
import gg.essential.universal.UScreen;
import org.apache.commons.lang3.exception.ExceptionUtils;

public class GUI {
    private static void open(WindowScreen screen) {
        try {
            Tick.schedule(1, () -> {
                try {
                    UScreen.displayScreen(screen);
                } catch (Exception e) {
                    ErrorReporter.reportError("GUI screen display failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
                }
            });
        } catch (Exception e) {
            ErrorReporter.reportError("GUI open scheduling failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
        }
    }
    public static void openConfig() {
        open(new ConfigGUI());
    }
    public static void openSpellGUI() {
        open(new SpellGUI());
    }
    public static void openEditorGUI() {
        open(new EditorGUI());
    }
}
