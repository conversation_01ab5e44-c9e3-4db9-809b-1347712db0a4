package com.ifallious.gui.components;

import com.ifallious.gui.screens.ConfigGUI;
import com.ifallious.gui.GUIUtils;
import gg.essential.elementa.UIComponent;
import gg.essential.elementa.components.UIImage;
import gg.essential.elementa.components.UIRoundedRectangle;
import gg.essential.elementa.components.UIText;
import gg.essential.elementa.constraints.*;
import gg.essential.elementa.constraints.animation.Animations;

import java.awt.*;
import java.util.ArrayList;
import java.util.List;

public class SideBarItem extends UIRoundedRectangle {
    private UIImage image;
    private UIComponent label;
    private boolean selected = false;
    private String itemText;
    private static List<SideBarItem> allItems = new ArrayList<>();
    private float scalingFactor = GUIUtils.getScale();
    
    public SideBarItem(float radius, String text, String icon, UIComponent parent, ConfigGUI instance) {
        super(radius);
        this.itemText = text;
        allItems.add(this);

        this.setX(new PixelConstraint(0f));
        this.setY(new SiblingConstraint(8f * scalingFactor));
        this.setWidth(new PixelConstraint(180f * scalingFactor));
        this.setHeight(new PixelConstraint(40f * scalingFactor));
        this.setColor(new Color(20, 22, 36, 230));
        this.setChildOf(parent);
        this.onMouseEnterRunnable(() -> {
            this.animateTo(this.makeAnimation().setHeightAnimation(Animations.OUT_EXP, 0.5f, new PixelConstraint(45f * scalingFactor)));
            if (selected) return;
            this.animateTo(this.makeAnimation().setColorAnimation(Animations.OUT_EXP, 0.5f, new ConstantColorConstraint(new Color(30, 32, 46, 230))));
        });
        this.onMouseLeaveRunnable(() -> {
            this.animateTo(this.makeAnimation().setHeightAnimation(Animations.OUT_EXP, 0.5f, new PixelConstraint(40f * scalingFactor)));
            if (selected) return;
            this.animateTo(this.makeAnimation().setColorAnimation(Animations.OUT_EXP, 0.5f, new ConstantColorConstraint(new Color(20, 22, 36, 230))));
        });
        if (ConfigGUI.selectedMenu.equals(text)) {
            select();
        }
        this.onMouseClick((component, event) -> {
            if (ConfigGUI.selectedMenu.equals(text)) {
                return null;
            } else {
                select();
                deselectAllExcept(this);
                ConfigGUI.selectedMenu = text;
                instance.updateContent(true);
            }
            return null;
        });
        image = (UIImage) UIImage.ofResource(icon)
                .setX(new PixelConstraint(6f * scalingFactor))
                .setY(new CenterConstraint())
                .setWidth(new PixelConstraint(25f * scalingFactor))
                .setHeight(new PixelConstraint(25f * scalingFactor))
                .setColor(new Color(255, 255, 255, 255))
                .setChildOf(this);
        label = new UIText(text, false)
                .setX(new SiblingConstraint(20f * scalingFactor))
                .setY(new CenterConstraint())
                .setWidth(new ScaledTextConstraint(1.6f * scalingFactor))
                .setHeight(new ScaledTextConstraint(1.6f * scalingFactor))
                .setChildOf(this);
    }
    
    public void select() {
        selected = true;
        this.animateTo(this.makeAnimation().setColorAnimation(Animations.OUT_EXP, 0.5f, new ConstantColorConstraint(new Color(150, 100, 255, 200))));
    }
    
    public void deselect() {
        selected = false;
        this.animateTo(this.makeAnimation().setColorAnimation(Animations.OUT_EXP, 0.5f, new ConstantColorConstraint(new Color(20, 22, 36, 230))));
    }
    
    private static void deselectAllExcept(SideBarItem exceptItem) {
        for (SideBarItem item : allItems) {
            if (item != exceptItem) {
                item.deselect();
            }
        }
    }
    
    public String getItemText() {
        return itemText;
    }

    public static void collapseAll() {
        for (SideBarItem item : allItems) {
            item.collapse();
        }
    }

    public static void expandAll() {
        for (SideBarItem item : allItems) {
            item.expand();
        }
    }

    public void collapse() {
        label.animateTo(label.makeAnimation().setColorAnimation(Animations.IN_OUT_EXP, 0.2f, new ConstantColorConstraint(new Color(255, 255, 255, 0))).onCompleteRunnable(() -> {label.hide();}));
        image.animateTo(image.makeAnimation().setXAnimation(Animations.IN_OUT_EXP, 0.5f, new PixelConstraint(3f * scalingFactor)));
        this.animateTo(this.makeAnimation().setWidthAnimation(Animations.IN_OUT_EXP, 0.5f, new PixelConstraint(32f * scalingFactor)));
    }

    public void expand() {
        label.unhide(true);
        label.animateTo(label.makeAnimation().setColorAnimation(Animations.IN_OUT_EXP, 0.2f, new ConstantColorConstraint(new Color(255, 255, 255, 255))));
        image.animateTo(image.makeAnimation().setXAnimation(Animations.IN_OUT_EXP, 0.5f, new PixelConstraint(6f * scalingFactor)));
        this.animateTo(this.makeAnimation().setWidthAnimation(Animations.IN_OUT_EXP, 0.5f, new PixelConstraint(180f * scalingFactor)));
    }
}
