package com.ifallious.gui.components;

import com.ifallious.features.spells.Spellmacro;
import com.ifallious.gui.GUIUtils;
import com.ifallious.gui.screens.SpellGUI;
import com.ifallious.utils.ErrorReporter;
import gg.essential.elementa.UIComponent;
import gg.essential.elementa.components.UIText;
import gg.essential.elementa.constraints.*;
import gg.essential.elementa.constraints.animation.Animations;
import gg.essential.universal.UMouse;

import java.awt.*;

import org.apache.commons.lang3.exception.ExceptionUtils;

import static com.ifallious.gui.GUIUtils.appear;

public class SpellcycleButtonComponent {
    private FloatingTooltipComponent tooltip;
    private UIComponent button;
    private String cycleName;
    private int cps;
    private boolean isHovered = false;

    public SpellcycleButtonComponent(UIComponent content, XConstraint x, YConstraint y, float width, float height, Color color, Color borderColor, String cycle, float textScale, Runnable runnable, SpellGUI instance) {
        this(content, x, y, width, height, color, borderColor, cycle, 0, textScale, runnable, instance);
    }

    public SpellcycleButtonComponent(UIComponent content, XConstraint x, YConstraint y, float width, float height, Color color, Color borderColor, String cycle, int cps, float textScale, Runnable runnable, SpellGUI instance) {
        this.cycleName = cycle.replaceAll("§[0-9a-fklmnor]", "").replaceAll("✦", "").trim();

        if (cps > 0) {
            this.cps = cps;
        } else {
            Spellmacro.CycleConfig config = Spellmacro.getCycleConfig(cycleName);
            this.cps = config.cps();
        }

        button = new RoundedRectangleWithBorder(10f, content, x, y, width, height, 2f, borderColor, color)
                .onMouseClick((component, event) -> {
                    runnable.run();
                    return null;
                });
        UIComponent background = button.getChildren().get(0);
        appear(button, 0.2f);

        createTooltip(instance.getWindow());

        button.onMouseEnterRunnable(() -> {
            button.animateTo(button.makeAnimation()
                .setColorAnimation(Animations.OUT_EXP, 0.5f, new ConstantColorConstraint(borderColor.brighter()))
                    .setWidthAnimation(Animations.OUT_EXP, 0.5f, new PixelConstraint(width + 8f))
                    .setHeightAnimation(Animations.OUT_EXP, 0.5f, new PixelConstraint(height + 8f))
            );
            background.animateTo(background.makeAnimation()
                .setColorAnimation(Animations.OUT_EXP, 0.5f, new ConstantColorConstraint(color.brighter()))
                    .setWidthAnimation(Animations.OUT_EXP, 0.5f, new PixelConstraint(width + 4f))
                    .setHeightAnimation(Animations.OUT_EXP, 0.5f, new PixelConstraint(height + 4f))
            );

            isHovered = true;
            showTooltip();

            startTooltipUpdates();
        });

        button.onMouseLeaveRunnable(() -> {
            button.animateTo(button.makeAnimation()
                .setColorAnimation(Animations.OUT_EXP, 0.5f, new ConstantColorConstraint(borderColor))
                    .setWidthAnimation(Animations.OUT_EXP, 0.5f, new PixelConstraint(width + 4f))
                    .setHeightAnimation(Animations.OUT_EXP, 0.5f, new PixelConstraint(height + 4f))
            );
            background.animateTo(background.makeAnimation()
                .setColorAnimation(Animations.OUT_EXP, 0.5f, new ConstantColorConstraint(color))
                    .setWidthAnimation(Animations.OUT_EXP, 0.5f, new PixelConstraint(width))
                    .setHeightAnimation(Animations.OUT_EXP, 0.5f, new PixelConstraint(height))
            );

            isHovered = false;
            hideTooltip();
        });

        new UIText(cycle)
                .setX(new CenterConstraint())
                .setY(new CenterConstraint())
                .setWidth(new ScaledTextConstraint(textScale))
                .setHeight(new ScaledTextConstraint(textScale))
                .setChildOf(button);
    }

    /**
     * Creates the floating tooltip with cycle information
     */
    private void createTooltip(UIComponent parent) {
        String[] cycleData = formatCycleData();

        // Create tooltip with adjusted dimensions for the expanded content
        tooltip = new FloatingTooltipComponent(
            cycleData,
            200f * GUIUtils.getScale(),
            11f * GUIUtils.getScale(),
            new Color(30, 30, 30, 230),
            Color.WHITE,
            parent
        );
    }

    /**
     * Formats the cycle data for display in the tooltip
     *
     * @return Array of formatted strings for the tooltip
     */
    private String[] formatCycleData() {
        // Get cycle configuration
        Spellmacro.CycleConfig config = Spellmacro.getCycleConfig(cycleName);
        String[][] cycle = config.cycle();
        int cooldown = config.cooldown();

        // Format cycle data - each spell on its own line
        String[] spellLines = new String[cycle.length];
        for (int i = 0; i < cycle.length; i++) {
            StringBuilder spellStr = new StringBuilder();
            spellStr.append("§7Spell §b").append(i + 1).append("§7: ");
            for (String spell : cycle[i]) {
                switch (spell) {
                    case "R" -> spellStr.append("§aR ");
                    case "L" -> spellStr.append("§cL ");
                    case "." -> spellStr.append("§ePowder Melee ");
                    default -> spellStr.append("§f").append(spell).append(" ");
                }
            }
            spellLines[i] = spellStr.toString();
        }

        // Create tooltip lines with more detailed information - each spell on its own line
        int headerLines = cooldown > 0 ? 4 : 3; // Title, CPS, [Cooldown], "Cycle:" header
        String[] lines = new String[headerLines + spellLines.length];

        lines[0] = "§l§6" + cycleName;
        lines[1] = "§fCPS: §b" + cps;

        if (cooldown > 0) {
            lines[2] = "§fCooldown: §c" + cooldown + " seconds";
            lines[3] = "§f§lCycle:";
        } else {
            lines[2] = "§f§lCycle:";
        }

        for (int i = 0; i < spellLines.length; i++) {
            lines[headerLines + i] = spellLines[i];
        }

        return lines;
    }

    /**
     * Shows the tooltip at the current mouse position
     */
    private void showTooltip() {
        if (tooltip != null) {
            tooltip.showTooltip((float) UMouse.getTrueX(), (float) UMouse.getTrueY());
        }
    }

    /**
     * Hides the tooltip
     */
    private void hideTooltip() {
        if (tooltip != null) {
            tooltip.hideTooltip();
        }
    }

    /**
     * Starts a thread to continuously update the tooltip position while hovered
     */
    private void startTooltipUpdates() {
        Thread updateThread = new Thread(() -> {
            try {
                while (isHovered && tooltip != null) {
                    float mouseX = (float) UMouse.getTrueX();
                    float mouseY = (float) UMouse.getTrueY();
                    tooltip.updatePosition(mouseX, mouseY);

                    Thread.sleep(16);
                }
            } catch (InterruptedException e) {
                ErrorReporter.reportError("SpellcycleButtonComponent thread interrupted", e.getMessage() + ExceptionUtils.getStackTrace(e));
            }
        });

        // Start the update thread
        updateThread.setDaemon(true);
        updateThread.start();
    }
}
