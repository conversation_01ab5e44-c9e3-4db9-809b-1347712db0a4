package com.ifallious.gui.components;

import gg.essential.elementa.components.input.UITextInput;
import gg.essential.universal.UChat;

import java.awt.*;

public class NumberInput extends UITextInput {
    public NumberInput(String placeholder) {
        super(placeholder);
    }
    public NumberInput(String placeholder, Boolean shadow) {
        super(placeholder, shadow);
    }
    public NumberInput(String placeholder, Boolean shadow, Color selectionBackgroundColor) {
        super(placeholder, shadow, selectionBackgroundColor);
    }
    public NumberInput(String placeholder, Boolean shadow, Color selectionBackgroundColor, Color selectionForegroundColor) {
        super(placeholder, shadow, selectionBackgroundColor, selectionForegroundColor);
    }
    public NumberInput(String placeholder, Boolean shadow, Color selectionBackgroundColor, Color selectionForegroundColor, Boolean allowInactiveSelection) {
        super(placeholder, shadow, selectionBackgroundColor, selectionForegroundColor, allowInactiveSelection);
    }
    public NumberInput(String placeholder, Boolean shadow, Color selectionBackgroundColor, Color selectionForegroundColor, Boolean allowInactiveSelection, Color inactiveSelectionBackgroundColor) {
        super(placeholder, shadow, selectionBackgroundColor, selectionForegroundColor, allowInactiveSelection, inactiveSelectionBackgroundColor);
    }
    public NumberInput(String placeholder, Boolean shadow, Color selectionBackgroundColor, Color selectionForegroundColor, Boolean allowInactiveSelection, Color inactiveSelectionBackgroundColor, Color inactiveSelectionForegroundColor) {
        super(placeholder, shadow, selectionBackgroundColor, selectionForegroundColor, allowInactiveSelection, inactiveSelectionBackgroundColor, inactiveSelectionForegroundColor);
    }
    public NumberInput(String placeholder, Boolean shadow, Color selectionBackgroundColor, Color selectionForegroundColor, Boolean allowInactiveSelection, Color inactiveSelectionBackgroundColor, Color inactiveSelectionForegroundColor, Color cursorColor) {
        super(placeholder, shadow, selectionBackgroundColor, selectionForegroundColor, allowInactiveSelection, inactiveSelectionBackgroundColor, inactiveSelectionForegroundColor, cursorColor);
    }

    @Override
    protected void commitTextOperation(TextOperation operation) {
        operation.redo();
        String text = this.getText();
        if (isTextValid(text)) {
            operation.undo();
            super.commitTextOperation(operation);
        } else operation.undo();
    }

    public boolean isTextValid(String text) {
        return text.matches("^[0-9]*$");
    }
}
