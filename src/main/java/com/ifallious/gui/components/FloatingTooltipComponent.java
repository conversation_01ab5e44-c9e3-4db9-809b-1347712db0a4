package com.ifallious.gui.components;

import com.ifallious.gui.GUIUtils;
import gg.essential.elementa.UIComponent;
import gg.essential.elementa.components.UIContainer;
import gg.essential.elementa.components.UIRoundedRectangle;
import gg.essential.elementa.components.UIText;
import gg.essential.elementa.constraints.CenterConstraint;
import gg.essential.elementa.constraints.PixelConstraint;
import gg.essential.elementa.constraints.ScaledTextConstraint;
import gg.essential.elementa.constraints.SiblingConstraint;
import gg.essential.universal.UScreen;

import java.awt.*;

/**
 * A floating tooltip component that can be positioned anywhere on the screen
 * and is not constrained by its parent's boundaries
 */
public class FloatingTooltipComponent extends UIContainer {
    private final UIRoundedRectangle background;
    private final UIContainer textContainer;

    /**
     * Creates a floating tooltip with multiple lines of text
     *
     * @param lines Array of text lines to display
     * @param width The width of the tooltip
     * @param lineHeight The height of each line
     * @param backgroundColor The background color of the tooltip
     * @param textColor The color of the text
     */
    public FloatingTooltipComponent(String[] lines, float width, float lineHeight, Color backgroundColor, Color textColor, UIComponent parent) {
        float totalHeight = lineHeight * lines.length + 16f;

        // Set the tooltip to be a child of the screen
        this.setChildOf(parent);

        // Initially position off-screen
        this.setX(new PixelConstraint(-1000f))
            .setY(new PixelConstraint(-1000f))
            .setWidth(new PixelConstraint(width))
            .setHeight(new PixelConstraint(totalHeight));

        // Create background
        background = (UIRoundedRectangle) new UIRoundedRectangle(15f)
            .setX(new CenterConstraint())
            .setY(new CenterConstraint())
            .setWidth(new PixelConstraint(width))
            .setHeight(new PixelConstraint(totalHeight))
            .setColor(backgroundColor)
            .setChildOf(this);

        // Create container for text lines with more padding
        textContainer = (UIContainer) new UIContainer()
            .setX(new CenterConstraint())
            .setY(new CenterConstraint())
            .setWidth(new PixelConstraint(width - 16f))
            .setHeight(new PixelConstraint(totalHeight - 16f))
            .setChildOf(background);

        // Add each line of text with improved spacing
        for (int i = 0; i < lines.length; i++) {
            float topMargin = 0f;

            if (i > 0 && lines[i].contains("§lCycle:")) {
                topMargin = 4f;
            } else if (i > 0) {
                topMargin = 2f;
            }

            new UIText(lines[i])
                .setX(new CenterConstraint())
                .setY(i == 0 ? new PixelConstraint(0f) : new SiblingConstraint(topMargin))
                    .setWidth(new ScaledTextConstraint(GUIUtils.getScale()))
                    .setHeight(new ScaledTextConstraint(GUIUtils.getScale()))
                .setColor(textColor)
                .setChildOf(textContainer);
        }
    }

    /**
     * Updates the position of the tooltip based on mouse coordinates
     *
     * @param mouseX The x-coordinate of the mouse
     * @param mouseY The y-coordinate of the mouse
     */
    public void updatePosition(float mouseX, float mouseY) {
        // Position the tooltip near the mouse cursor
        // Add offset to prevent tooltip from being under the cursor
        float offsetX = 10f;
        float offsetY = 10f;

        // Get screen dimensions
        float screenWidth = 1920f;
        float screenHeight = 1080f;
        if (UScreen.getCurrentScreen() != null){
            screenWidth = UScreen.getCurrentScreen().width;
            screenHeight = UScreen.getCurrentScreen().height;
        }

        // Calculate tooltip position
        float tooltipX = mouseX + offsetX;
        float tooltipY = mouseY + offsetY;

        // Adjust position if tooltip would go off-screen
        if (tooltipX + this.getWidth() > screenWidth) {
            tooltipX = mouseX - this.getWidth() - offsetX;
        }

        if (tooltipY + this.getHeight() > screenHeight) {
            tooltipY = mouseY - this.getHeight() - offsetY;
        }

        this.setX(new PixelConstraint(tooltipX))
            .setY(new PixelConstraint(tooltipY));
    }

    /**
     * Shows the tooltip at the specified position
     *
     * @param mouseX The x-coordinate of the mouse
     * @param mouseY The y-coordinate of the mouse
     */
    public void showTooltip(float mouseX, float mouseY) {
        updatePosition(mouseX, mouseY);
    }

    /**
     * Hides the tooltip
     */
    public void hideTooltip() {
        updatePosition(-1000f, -1000f);
    }

    /**
     * Updates the text content of the tooltip
     *
     * @param lines Array of text lines to display
     * @param lineHeight The height of each line
     * @param textColor The color of the text
     */
    public void updateContent(String[] lines, float lineHeight, Color textColor) {
        float totalHeight = lineHeight * lines.length + 16f;

        // Update tooltip height
        this.setHeight(new PixelConstraint(totalHeight));
        background.setHeight(new PixelConstraint(totalHeight));
        textContainer.setHeight(new PixelConstraint(totalHeight - 16f));

        // Clear existing text
        textContainer.clearChildren();

        // Add new text lines with improved spacing
        for (int i = 0; i < lines.length; i++) {
            float topMargin = 0f;

            // Add extra spacing before the "Cycle:" header
            if (i > 0 && lines[i].contains("§lCycle:")) {
                topMargin = 4f;
            } else if (i > 0) {
                topMargin = 2f;
            }

            new UIText(lines[i])
                .setX(new CenterConstraint())
                .setY(i == 0 ? new PixelConstraint(0f) : new SiblingConstraint(topMargin))
                .setColor(textColor)
                .setChildOf(textContainer);
        }
    }
}
