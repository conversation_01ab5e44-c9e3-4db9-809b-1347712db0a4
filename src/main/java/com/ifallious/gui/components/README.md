# Config Screen Framework

This framework provides a simple way to create configuration screens for different categories in the Wynnutils mod.

## Overview

The framework consists of the following main classes:

1. `ConfigOption` - Represents a configuration option with metadata for UI generation.
2. `CategoryConfigScreen` - Abstract base class for category-specific configuration screens.
3. `ConfigScreen` - Extends CategoryConfigScreen for backward compatibility.

## How to Use

### Creating a New Category Screen

To create a new category screen, extend the `CategoryConfigScreen` class and implement the `initOptions()` method:

```java
public class MyCategoryScreen extends CategoryConfigScreen {
    
    public MyCategoryScreen(UIComponent parent) {
        super(parent, "My Category");
    }
    
    @Override
    protected void initOptions() {
        // Add your options here
        addOption(new ConfigOption(
                "My Toggle Option",
                "myToggleOption",
                "This is a toggle option.",
                true
        ));
        
        addOption(new ConfigOption(
                "My Numeric Option",
                "myNumericOption",
                "This is a numeric option.",
                1.0,
                0.0,
                2.0
        ));
    }
}
```

### Adding Options

The `ConfigOption` class supports three types of options:

1. **Boolean Options** - Toggle switches
   ```java
   addOption(new ConfigOption(
           "Display Name",
           "configId",
           "Description of the option.",
           true  // default value
   ));
   ```

2. **Numeric Options** - Sliders
   ```java
   addOption(new ConfigOption(
           "Display Name",
           "configId",
           "Description of the option.",
           1.0,  // default value
           0.0,  // minimum value
           2.0   // maximum value
   ));
   ```

3. **String Options** - Text fields (currently not fully supported)
   ```java
   addOption(new ConfigOption(
           "Display Name",
           "configId",
           "Description of the option.",
           "Default text"
   ));
   ```

### Registering the Category Screen

To make your category screen accessible from the main config GUI, update the `ConfigGUI` class:

1. Import your category screen class:
   ```java
   import com.ifallious.gui.components.MyCategoryScreen;
   ```

2. Add a sidebar item for your category:
   ```java
   new SideBarItem(5f, "MyCategory", "/assets/wynnutils/icon.png", sidebarItems, this);
   ```

3. Update the `updateContent()` method to create your screen when selected:
   ```java
   case "MyCategory":
       new MyCategoryScreen(content);
       break;
   ```

## Configuration System Integration

The framework automatically integrates with the Wynnutils configuration system:

- Boolean options use `FeatureConfig.isEnabled()` and `FeatureConfig.toggle()`
- Numeric options use `ConfigManager.getSetting()` and `ConfigManager.setSetting()`

Make sure your option IDs match the field names in `ConfigFeatures` or `ConfigSettings` classes.
