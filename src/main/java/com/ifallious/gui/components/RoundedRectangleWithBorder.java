package com.ifallious.gui.components;

import gg.essential.elementa.UIComponent;
import gg.essential.elementa.components.UIRoundedRectangle;
import gg.essential.elementa.constraints.*;

import java.awt.*;

public class RoundedRectangleWithBorder extends UIRoundedRectangle {
    public RoundedRectangleWithBorder(float radius, UIComponent parent, XConstraint x, YConstraint y, float width, float height, float borderWidth, Color borderColor, Color backgroundColor) {
        super(radius);
        this.setX(x);
        this.setY(y);
        this.setWidth(new PixelConstraint(width  + borderWidth * 2));
        this.setHeight(new PixelConstraint(height + borderWidth * 2));
        this.setColor(borderColor);
        this.setChildOf(parent);
        UIComponent background = new UIRoundedRectangle(radius)
                .setX(new CenterConstraint())
                .setY(new CenterConstraint())
                .setWidth(new PixelConstraint(width))
                .setHeight(new PixelConstraint(height))
                .setColor(backgroundColor)
                .setChildOf(this);
    }
}
