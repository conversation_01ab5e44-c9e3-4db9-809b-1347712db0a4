package com.ifallious.gui.components;

import com.ifallious.gui.GUIUtils;
import gg.essential.elementa.UIComponent;
import gg.essential.elementa.components.*;
import gg.essential.elementa.constraints.CenterConstraint;
import gg.essential.elementa.constraints.PixelConstraint;
import gg.essential.elementa.constraints.ScaledTextConstraint;


import java.awt.Color;
import java.util.function.Consumer;
import java.text.DecimalFormat;

import static com.ifallious.gui.GUIUtils.appear;

public class SliderComponent extends UIContainer {
    private float percentage;
    private double minValue = 0.0;
    private double maxValue = 1.0;
    private Consumer<Double> onValueChange = p -> {};
    private Consumer<Double> onValueChanged = p -> {};
    private boolean dragging = false;
    private float grabOffset = 0f;
    private float scalingFactor = GUIUtils.getScale();
    private static final DecimalFormat df = new DecimalFormat("#.##");

    private final UIComponent outerBox;
    private final UIComponent completionBox;
    private final UIComponent grabBox;
    private final UIText valueLabel;

    private float SLIDER_HEIGHT = 20f;
    private float SLIDER_WIDTH = 80f;
    private float GRAB_SIZE = 15f;

    /**
     * Creates a slider with percentage values (0.0 to 1.0)
     */
    public SliderComponent(float initialValue, UIComponent parent, boolean shouldAppear) {
        this(initialValue, parent, shouldAppear, 20f, 80f);
    }

    /**
     * Creates a slider with percentage values (0.0 to 1.0) and custom dimensions
     */
    public SliderComponent(float initialValue, UIComponent parent, boolean shouldAppear, float height, float width) {
        this(initialValue, 0.0, 1.0, parent, shouldAppear, height, width);
    }

    /**
     * Creates a slider with custom min/max values and dimensions
     */
    public SliderComponent(double initialValue, double minValue, double maxValue, UIComponent parent, boolean shouldAppear, float height, float width) {
        this.minValue = minValue;
        this.maxValue = maxValue;
        this.percentage = valueToPercentage(initialValue);
        this.SLIDER_HEIGHT = height;
        this.SLIDER_WIDTH = width;
        this.GRAB_SIZE = height * 0.75f;

        this.setX(new CenterConstraint())
            .setY(new CenterConstraint())
            .setWidth(new PixelConstraint(SLIDER_WIDTH + GRAB_SIZE))
            .setHeight(new PixelConstraint(SLIDER_HEIGHT));
        this.setChildOf(parent);

        // Outer box (track)
        outerBox = new UIRoundedRectangle(SLIDER_HEIGHT / 4f)
            .setX(new PixelConstraint(GRAB_SIZE / 2f))
            .setY(new CenterConstraint())
            .setWidth(new PixelConstraint(SLIDER_WIDTH))
            .setHeight(new PixelConstraint(SLIDER_HEIGHT / 2f))
            .setColor(new Color(40, 40, 40))
                .setChildOf(this);

        // Completion box (filled part)
        completionBox = new UIRoundedRectangle(SLIDER_HEIGHT / 4f)
            .setX(new PixelConstraint(0f))
            .setY(new PixelConstraint(0f))
            .setWidth(new PixelConstraint(SLIDER_WIDTH * percentage))
            .setHeight(new PixelConstraint(SLIDER_HEIGHT / 2f))
            .setColor(new Color(0, 120, 255));
        outerBox.addChild(completionBox);

        // Grab box (draggable handle)
        grabBox = new UIRoundedRectangle(GRAB_SIZE / 2f)
            .setX(new PixelConstraint(getGrabX()))
            .setY(new CenterConstraint())
            .setWidth(new PixelConstraint(GRAB_SIZE))
            .setHeight(new PixelConstraint(GRAB_SIZE))
            .setColor(Color.WHITE);
        this.addChild(grabBox);

        valueLabel = (UIText) new UIText(formatValue(getCurrentValue()))
            .setX(new CenterConstraint())
            .setY(new PixelConstraint(SLIDER_HEIGHT + 5))
                .setWidth(new ScaledTextConstraint(1.5f * scalingFactor))
                .setHeight(new ScaledTextConstraint(1.5f * scalingFactor))
            .setChildOf(this);
        if(shouldAppear) {
            appear(valueLabel, 0.3f);
            appear(grabBox, 0.3f);
            appear(completionBox, 0.3f);
            appear(outerBox, 0.3f);
        }

        grabBox.onMouseClick((component, event) -> {
            if (event.getMouseButton() == 0) { // Left click
                dragging = true;
                grabOffset = event.getRelativeX() - (grabBox.getWidth() / 2f);
                event.stopPropagation();
            }
            return null;
        });
        grabBox.onMouseRelease((component) -> {
            if (dragging) {
                dragging = false;
                grabOffset = 0f;
                onValueChanged.accept(getCurrentValue());
            }
            return null;
        });
        grabBox.onMouseDrag((component, mouseX, mouseY, button) -> {
            if (!dragging) return null;
            float clamped = (mouseX + grabBox.getLeft() - grabOffset);
            float newPercentage = (clamped - outerBox.getLeft()) / outerBox.getWidth();
            setCurrentPercentage(newPercentage, true);
            return null;
        });

        outerBox.onMouseClick((component, event) -> {
            if (event.getMouseButton() == 0) {
                float localX = event.getRelativeX();
                float clamped = clamp(localX, 0f, SLIDER_WIDTH);
                float newPercentage = clamped / SLIDER_WIDTH;
                setCurrentPercentage(newPercentage, true);
                dragging = true;
                onValueChanged.accept(getCurrentValue());
            }
            return null;
        });
    }

    private float getGrabX() {
        return (SLIDER_WIDTH * percentage) - (GRAB_SIZE / 2f) + (GRAB_SIZE / 2f);
    }

    /**
     * Gets the current percentage (0.0 to 1.0)
     */
    public float getCurrentPercentage() {
        return percentage;
    }

    /**
     * Gets the current actual value (between minValue and maxValue)
     */
    public double getCurrentValue() {
        return percentageToValue(percentage);
    }

    /**
     * Converts a percentage (0.0 to 1.0) to an actual value
     */
    private double percentageToValue(float percentage) {
        return minValue + (percentage * (maxValue - minValue));
    }

    /**
     * Converts an actual value to a percentage (0.0 to 1.0)
     */
    private float valueToPercentage(double value) {
        if (maxValue == minValue) return 0f; // Prevent division by zero
        return (float) clamp((value - minValue) / (maxValue - minValue), 0.0, 1.0);
    }

    /**
     * Formats the value for display
     */
    private String formatValue(double value) {
        return df.format(value);
    }

    /**
     * Sets the current percentage (0.0 to 1.0)
     */
    public void setCurrentPercentage(float newPercentage, boolean callListener) {
        percentage = clamp(newPercentage, 0f, 1f);
        completionBox.setWidth(new PixelConstraint(SLIDER_WIDTH * percentage));
        grabBox.setX(new PixelConstraint(getGrabX()));
        valueLabel.setText(formatValue(getCurrentValue()));
        if (callListener) {
            onValueChange.accept(getCurrentValue());
        }
    }

    /**
     * Sets the current actual value (between minValue and maxValue)
     */
    public void setCurrentValue(double value, boolean callListener) {
        setCurrentPercentage(valueToPercentage(value), callListener);
    }

    /**
     * Sets a listener for value changes during dragging
     */
    public void setOnValueChange(Consumer<Double> listener) {
        this.onValueChange = listener != null ? listener : p -> {};
    }

    /**
     * Sets a listener for value changes after dragging is complete
     */
    public void setOnValueChanged(Consumer<Double> listener) {
        this.onValueChanged = listener != null ? listener : p -> {};
    }

    private static float clamp(float value, float min, float max) {
        return Math.max(min, Math.min(max, value));
    }

    private static double clamp(double value, double min, double max) {
        return Math.max(min, Math.min(max, value));
    }
}