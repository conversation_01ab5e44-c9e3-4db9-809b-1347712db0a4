package com.ifallious.gui.components;

import com.ifallious.gui.GUIUtils;
import gg.essential.elementa.UIComponent;
import gg.essential.elementa.components.ScrollComponent;
import gg.essential.elementa.components.UIContainer;
import gg.essential.elementa.constraints.CenterConstraint;
import gg.essential.elementa.constraints.PixelConstraint;
import gg.essential.elementa.markdown.MarkdownComponent;

public class InfoContent extends UIContainer {
    public InfoContent(UIComponent parent) {
        super();
        float scaleFactor = GUIUtils.getScale();
        this.setX(new CenterConstraint());
        this.setY(new CenterConstraint());
        this.setWidth(new PixelConstraint(780f * scaleFactor));
        this.setHeight(new PixelConstraint(700f * scaleFactor));
        this.setChildOf(parent);

        UIComponent scroll = new ScrollComponent("Empty :(", 0f)
                .setX(new CenterConstraint())
                .setY(new CenterConstraint())
                .setWidth(new PixelConstraint(760f * scaleFactor))
                .setHeight(new PixelConstraint(680f * scaleFactor))
                .setChildOf(this);
        new MarkdownComponent("""
                # Wynnutils
                Wynnutils is a mod for Wynncraft that adds various quality of life features.
                ## Features
                - Spellmacro
                - War features
                - Notifications
                - And more!
                ## Guide
                ### Spellmacro
                The spellmacro feature allows you to automatically cast spells by pressing/holding a key.
                This key can be assigned in the Minecraft settings.
                To use it, you need to have a cycle set up. A cycle is a list of spells that will be cast in order.
                You can set up cycles in the Cycle Editor.
                Cycles are structured as follows:
                The ` Cycle ` array contains the spells that will be cast in order.
                The ` CPS ` value is the number of casts per second.
                You can add as many cycles as you want, and they will be available in the Spellmacro menu.
                ` R = Right Click `
                ` L = Left Click `
                ` . = Powder Melee `
                And you can switch between cycles using the command ` /wynnutils spellcycle switch <Cycle> ` or the Spellmacro menu.
                ## Changelog
                ### 0.0.1
                - Initial Release
                """)
                .setX(new CenterConstraint())
                .setY(new PixelConstraint(0f))
                .setWidth(new PixelConstraint(740f * scaleFactor))
                .setHeight(new PixelConstraint(660f * scaleFactor))
                .setTextScale(new PixelConstraint(4f))
                .setChildOf(scroll);
    }
}
