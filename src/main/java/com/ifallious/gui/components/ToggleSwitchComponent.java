package com.ifallious.gui.components;

import com.ifallious.utils.config.FeatureConfig;
import gg.essential.elementa.UIComponent;
import gg.essential.elementa.components.UICircle;
import gg.essential.elementa.components.UIRoundedRectangle;
import gg.essential.elementa.constraints.CenterConstraint;
import gg.essential.elementa.constraints.ConstantColorConstraint;
import gg.essential.elementa.constraints.PixelConstraint;
import gg.essential.elementa.constraints.animation.Animations;

import java.awt.*;
import java.util.function.Consumer;

import static com.ifallious.gui.GUIUtils.appear;

public class ToggleSwitchComponent extends UIRoundedRectangle {
    private boolean state;
    private UIComponent circle;
    private Consumer<Boolean> stateChangeHandler;

    public ToggleSwitchComponent(UIComponent parent, boolean initialState, float height, float width, boolean shouldAppear) {
        this(parent, initialState, height, width, shouldAppear, null);
    }

    public ToggleSwitchComponent(UIComponent parent, boolean initialState, float height, float width, boolean shouldAppear, String id) {
        super(height/1.75f);
        this.state = initialState;
        this.setX(new CenterConstraint());
        this.setY(new CenterConstraint());
        this.setWidth(new PixelConstraint(width));
        this.setHeight(new PixelConstraint(height));
        this.setColor(state ? new Color(60, 200, 80, 255) : new Color(180, 60, 60, 255));
        this.setChildOf(parent);
        
        float circleRadius = height * 0.4f;
        float circleOffset = circleRadius + height * 0.1f;
        float circleEndX = width - circleOffset;

        circle = new UICircle(circleRadius)
                .setX(new PixelConstraint(state ? circleEndX : circleOffset))
                .setY(new CenterConstraint())
                .setChildOf(this);
                
        this.onMouseClick((component, event) -> {
            if (id != null) {
                FeatureConfig.toggle(id);
                state = FeatureConfig.isEnabled(id);
            } else {
                state = !state;
                if (stateChangeHandler != null) {
                    stateChangeHandler.accept(state);
                }
            }
            
            updateVisuals(circleOffset, circleEndX);
            return null;
        });
        if (shouldAppear) {appear(circle, 0.3f);
            appear(this, 0.3f);
        ;}

    }
    
    private void updateVisuals(float startX, float endX) {
        this.animateTo(this.makeAnimation().setColorAnimation(
            Animations.OUT_EXP, 0.5f, 
            new ConstantColorConstraint(state ? new Color(60, 200, 80, 255) : new Color(180, 60, 60, 255)))
        );
        
        circle.animateTo(circle.makeAnimation().setXAnimation(
            Animations.OUT_EXP, 0.5f, 
            new PixelConstraint(state ? endX : startX)
        ));
    }
}
