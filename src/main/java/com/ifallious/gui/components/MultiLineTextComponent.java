package com.ifallious.gui.components;

import gg.essential.elementa.UIComponent;
import gg.essential.elementa.components.UIContainer;
import gg.essential.elementa.components.UIText;
import gg.essential.elementa.constraints.*;
import gg.essential.elementa.constraints.animation.Animations;
import gg.essential.elementa.font.ElementaFonts;

import java.awt.Color;
import java.util.ArrayList;
import java.util.List;

/**
 * A custom component that wraps text into multiple UITexts to fit a given width.
 */
public class MultiLineTextComponent extends UIContainer {
    private final List<UIComponent> lines = new ArrayList<>();

    /**
     * @param text The text to wrap
     * @param maxWidth The maximum width in pixels for each line
     * @param textScale The scale for the text (1.0 = normal)
     * @param color The color of the text
     */
    public MultiLineTextComponent(String text, float maxWidth, float textScale, Color color, UIComponent parent, Boolean shouldAppear) {
        super();
        this.setChildOf(parent);
        List<String> wrappedLines = wrapText(text, maxWidth, textScale);
        for (String line : wrappedLines) {
            UIComponent uiText = new UIText(line)
                .setX(new PixelConstraint(0f))
                .setY(new SiblingConstraint(3f))
                    .setWidth(new ScaledTextConstraint(textScale))
                    .setHeight(new ScaledTextConstraint(textScale))
                .setColor(new Color(0, 0, 0, 0))
                .setChildOf(this);
            if (shouldAppear) {
                uiText.animateTo(uiText.makeAnimation().setColorAnimation(Animations.IN_OUT_EXP, 0.3f, new ConstantColorConstraint(color)));
            } else {
                uiText.setColor(color);
            }
            lines.add(uiText);
        }
        this.setWidth(new PixelConstraint(maxWidth));
        this.setHeight(new ChildBasedSizeConstraint(1f));
    }

    // Simple word wrap algorithm
    private List<String> wrapText(String text, float maxWidth, float textScale) {
        List<String> result = new ArrayList<>();
        String[] words = text.split(" ");
        StringBuilder line = new StringBuilder();
        for (String word : words) {
            String testLine = line.isEmpty() ? word : line + " " + word;
            float testWidth = estimateTextWidth(testLine, textScale);
            if (testWidth > maxWidth && !line.isEmpty()) {
                result.add(line.toString());
                line = new StringBuilder(word);
            } else {
                if (!line.isEmpty()) line.append(" ");
                line.append(word);
            }
        }
        if (!line.isEmpty()) {
            result.add(line.toString());
        }
        return result;
    }

    // Estimate text width (roughly 8px per character at scale 1.0)
    private float estimateTextWidth(String text, float textScale) {
        return text.length() * 5.5f * textScale;
    }
} 