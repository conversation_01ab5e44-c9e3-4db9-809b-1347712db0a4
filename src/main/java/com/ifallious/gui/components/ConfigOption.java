package com.ifallious.gui.components;

/**
 * Represents a configuration option with metadata for UI generation.
 */
public class ConfigOption {
    // Option types
    public static final int TYPE_BOOLEAN = 0;
    public static final int TYPE_NUMERIC = 1;
    public static final int TYPE_STRING = 2;
    
    private String displayName;
    private String id;
    private String description;
    private int type;
    
    // For boolean options
    private boolean defaultBooleanValue;
    
    // For numeric options
    private double defaultNumericValue;
    private double minValue;
    private double maxValue;
    
    // For string options
    private String defaultStringValue;
    private double lines;
    
    /**
     * Creates a new boolean configuration option.
     *
     * @param displayName The display name shown in the UI
     * @param id The internal ID used to access the option in the config
     * @param description The description shown in the UI
     * @param defaultValue The default boolean value
     */
    public ConfigOption(String displayName, String id, String description, boolean defaultValue) {
        this.displayName = displayName;
        this.id = id;
        this.description = description;
        this.type = TYPE_BOOLEAN;
        this.defaultBooleanValue = defaultValue;
    }
    
    /**
     * Creates a new numeric configuration option.
     *
     * @param displayName The display name shown in the UI
     * @param id The internal ID used to access the option in the config
     * @param description The description shown in the UI
     * @param defaultValue The default numeric value
     * @param minValue The minimum allowed value
     * @param maxValue The maximum allowed value
     */
    public ConfigOption(String displayName, String id, String description, double defaultValue, double minValue, double maxValue) {
        this.displayName = displayName;
        this.id = id;
        this.description = description;
        this.type = TYPE_NUMERIC;
        this.defaultNumericValue = defaultValue;
        this.minValue = minValue;
        this.maxValue = maxValue;
    }
    
    /**
     * Creates a new string configuration option.
     *
     * @param displayName The display name shown in the UI
     * @param id The internal ID used to access the option in the config
     * @param description The description shown in the UI
     * @param defaultValue The default string value
     */
    public ConfigOption(String displayName, String id, String description, String defaultValue, double lines) {
        this.displayName = displayName;
        this.id = id;
        this.description = description;
        this.type = TYPE_STRING;
        this.defaultStringValue = defaultValue;
        this.lines = lines;
    }
    
    /**
     * Get the display name of the option.
     * @return The display name
     */
    public String getDisplayName() {
        return displayName;
    }
    
    /**
     * Get the internal ID of the option.
     * @return The ID
     */
    public String getId() {
        return id;
    }
    
    /**
     * Get the description of the option.
     * @return The description
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * Get the type of the option.
     * @return The type (TYPE_BOOLEAN, TYPE_NUMERIC, or TYPE_STRING)
     */
    public int getType() {
        return type;
    }
    
    /**
     * Get the default boolean value.
     * @return The default boolean value
     */
    public boolean getDefaultBooleanValue() {
        return defaultBooleanValue;
    }
    
    /**
     * Get the default numeric value.
     * @return The default numeric value
     */
    public double getDefaultNumericValue() {
        return defaultNumericValue;
    }
    
    /**
     * Get the minimum allowed numeric value.
     * @return The minimum value
     */
    public double getMinValue() {
        return minValue;
    }
    
    /**
     * Get the maximum allowed numeric value.
     * @return The maximum value
     */
    public double getMaxValue() {
        return maxValue;
    }
    
    /**
     * Get the default string value.
     * @return The default string value
     */
    public String getDefaultStringValue() {
        return defaultStringValue;
    }

    /**
     * Get the amount of Lines the Input should have
     * @return Amount of lines
     */
    public double getLines() {return lines;}
}
