package com.ifallious.features.spells;

import com.ifallious.Wynnutils;
import com.ifallious.event.GlobalEventBus;
import com.ifallious.event.TickEvent;
import com.ifallious.utils.featureutils.SpellmacroUtils;
import com.ifallious.utils.config.ConfigManager;
import com.ifallious.utils.ErrorReporter;
import meteordevelopment.orbit.EventHandler;
import com.wynntils.core.components.Models;
import com.wynntils.models.abilities.type.ShamanTotem;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

public class TotemCast {
    public static Boolean totemcasted = true;
    public TotemCast() {
        GlobalEventBus.subscribe(this);
    }
    @EventHandler
    public void onTick(TickEvent event) {
        try {
            if (Boolean.TRUE.equals(ConfigManager.getFeature("recastTotem"))) {
                if (getTotemTime() > 3) {
                    totemcasted = false;
                }
                if (!totemcasted && getTotemTime() == 2 && Wynnutils.mc.player != null) {
                    try {
                        SpellmacroUtils.add(Arrays.asList("R", "L", "R"));
                    } catch (Exception e) {
                        ErrorReporter.reportError("TotemCast spell addition failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
                    }
                    totemcasted = true;
                }
            }
        } catch (Exception e) {
            ErrorReporter.reportError("TotemCast tick event failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
        }
    }
    public static int getTotemTime() {
        try {
            List<ShamanTotem> totems = Models.ShamanTotem.getActiveTotems();
            if (!totems.isEmpty()) {
                ShamanTotem totem = totems.stream().filter(Objects::nonNull).findFirst().orElse(null);
                if (totem != null) {
                    return totem.getTime();
                }
            }
            return 0;
        } catch (Exception e) {
            ErrorReporter.reportError("TotemCast getTotemTime failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
            return 0;
        }
    }
}
