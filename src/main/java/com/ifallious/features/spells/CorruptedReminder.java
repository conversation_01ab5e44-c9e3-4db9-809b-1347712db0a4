package com.ifallious.features.spells;

import com.ifallious.event.GlobalEventBus;
import com.ifallious.event.TickEvent;
import com.ifallious.utils.minecraft.Tick;
import com.ifallious.utils.config.ConfigManager;
import com.ifallious.utils.ErrorReporter;
import com.ifallious.utils.wynntils.StatusEffect;
import com.ifallious.utils.wynntils.StatusEffectUtils;
import gg.essential.universal.wrappers.UPlayer;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.sound.SoundCategory;
import net.minecraft.sound.SoundEvents;
import com.ifallious.utils.Utils;
import org.apache.commons.lang3.exception.ExceptionUtils;

public class CorruptedReminder {
    public CorruptedReminder() {
        GlobalEventBus.subscribe(this);
    }
    private int lastSecond = -1;

    @EventHandler
    public void onTick(TickEvent event) {
        try {
            if (Boolean.TRUE.equals(ConfigManager.getFeature("corruptedCooldown"))) {
                StatusEffect Corrupted = StatusEffectUtils.getStatusEffect("Corrupted");
                if (Corrupted == null) {
                    lastSecond = -1;
                    return;
                }

                if (Corrupted.isIncluded() && Corrupted.getSeconds() < 5 && Corrupted.getMinutes() == 0) {
                    if (Corrupted.getSeconds() != lastSecond) {
                        lastSecond = Corrupted.getSeconds();
                        try {
                            Utils.displayTitle("§4Corrupted in: " + Corrupted.getFormattedTime());
                        } catch (Exception e) {
                            ErrorReporter.reportError("CorruptedReminder title display failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
                        }
                    }

                    if (Corrupted.getSeconds() == 0) {
                        Tick.schedule(10, () -> {
                            try {
                                UPlayer.getPlayer().playSoundToPlayer(SoundEvents.BLOCK_AMETHYST_BLOCK_BREAK, SoundCategory.MASTER, 1.0f, 1.0f);
                            } catch (Exception e) {
                                ErrorReporter.reportError("CorruptedReminder sound play failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
                            }
                        });
                    }
                } else {
                    lastSecond = -1;
                }
            }
        } catch (Exception e) {
            ErrorReporter.reportError("CorruptedReminder tick event failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
        }
    }
}
