package com.ifallious.features.spells;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonObject;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.ifallious.Wynnutils;
import com.ifallious.utils.ErrorReporter;
import com.ifallious.utils.minecraft.PacketUtils;
import com.ifallious.utils.featureutils.SpellmacroUtils;
import com.ifallious.utils.config.ConfigManager;
import net.fabricmc.fabric.api.client.event.lifecycle.v1.ClientTickEvents;
import net.minecraft.client.option.KeyBinding;
import net.fabricmc.fabric.api.client.keybinding.v1.KeyBindingHelper;
import net.minecraft.client.util.InputUtil;
import gg.essential.universal.UChat;
import net.minecraft.network.packet.Packet;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.lwjgl.glfw.GLFW;

import java.nio.file.Files;
import java.nio.file.StandardCopyOption;

public class Spellmacro {
    private static String[][] cycle; // Array of String arrays
    private static int spellIndex;
    private static long lastSpell;
    private static Map<String, CycleConfig> cycles;
    private static String currentCycle;
    private static int cps;
    private static final File CYCLES_FILE = new File(Wynnutils.FOLDER, "Cycles.json");
    private static KeyBinding spellKey;

    public Spellmacro() {
        try {

            spellIndex = 0;
            cycles = new HashMap<>();
            cps = 10;
            loadCycles();
            if (cycles.isEmpty()) {
                cycle = new String[][] {{"R","R","R"},{"R", "R", "L"}};
            } else {
                String savedCycle = ConfigManager.getSelectedCycle();
                if (savedCycle != null && !savedCycle.isEmpty() && cycles.containsKey(savedCycle)) {
                    currentCycle = savedCycle;
                } else {
                    currentCycle = cycles.keySet().iterator().next();
                }
                cycle = cycles.get(currentCycle).cycle();
            }
            Thread packet_thread = new Thread(new PacketThread());
            packet_thread.start();
            spellKey = KeyBindingHelper.registerKeyBinding(new KeyBinding("Spellmacro", InputUtil.Type.KEYSYM, GLFW.GLFW_KEY_UNKNOWN, "Wynnutils"));
            ClientTickEvents.END_CLIENT_TICK.register(client -> {
                try {
                    while (spellKey.isPressed() && SpellmacroUtils.size() < 3) {
                        addSpell();
                    }
                } catch (Exception e) {
                    ErrorReporter.reportError("Spellmacro key binding event failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
                }
            });
        } catch (Exception e) {
            ErrorReporter.reportError("Spellmacro initialization failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
        }
    }

    public static void loadCycles() {
        if (!CYCLES_FILE.exists()) {
            createDefaultCyclesFile();
        }

        try (FileReader reader = new FileReader(CYCLES_FILE)) {
            Gson gson = new Gson();
            JsonObject json = gson.fromJson(reader, JsonObject.class);
            cycles.clear();
            for (Map.Entry<String, JsonElement> entry : json.entrySet()) {
                String cycleName = entry.getKey();
                JsonObject cycleObj = entry.getValue().getAsJsonObject();

                JsonArray cycleArray = cycleObj.getAsJsonArray("Cycle");
                String[][] cycleData = new String[cycleArray.size()][];

                for (int i = 0; i < cycleArray.size(); i++) {
                    JsonArray spellArray = cycleArray.get(i).getAsJsonArray();
                    String[] spells = new String[spellArray.size()];

                    for (int j = 0; j < spellArray.size(); j++) {
                        spells[j] = spellArray.get(j).getAsString();
                    }

                    cycleData[i] = spells;
                }

                int cps = cycleObj.has("CPS") ? cycleObj.get("CPS").getAsInt() : 10;
                int cooldown = cycleObj.has("Cooldown") ? cycleObj.get("Cooldown").getAsInt() : 0;

                cycles.put(cycleName, new CycleConfig(cycleData, cps, cooldown));
            }

            UChat.chat("Loaded " + cycles.size() + " spell cycles");
        } catch (IOException e) {
            Wynnutils.LOGGER.error("Failed to load cycles from " + CYCLES_FILE.getAbsolutePath(), e);
            ErrorReporter.reportError("Failed to load cycles from file", e.getMessage() + ExceptionUtils.getStackTrace(e));
        }
    }

    private static void createDefaultCyclesFile() {
        try {
            CYCLES_FILE.getParentFile().mkdirs();

            Map<String, CycleConfig> defaultCycles = new HashMap<>();
            Gson gson = new GsonBuilder().setPrettyPrinting().create();
            JsonObject json = new JsonObject();

            for (Map.Entry<String, CycleConfig> entry : defaultCycles.entrySet()) {
                JsonObject cycleObj = new JsonObject();
                JsonArray cycleArray = new JsonArray();

                for (String[] spells : entry.getValue().cycle()) {
                    JsonArray spellArray = new JsonArray();
                    for (String spell : spells) {
                        spellArray.add(spell);
                    }
                    cycleArray.add(spellArray);
                }

                cycleObj.add("Cycle", cycleArray);
                cycleObj.addProperty("CPS", entry.getValue().cps());
                if (entry.getValue().cooldown() > 0) {
                    cycleObj.addProperty("Cooldown", entry.getValue().cooldown());
                }

                json.add(entry.getKey(), cycleObj);
            }

            try (FileWriter writer = new FileWriter(CYCLES_FILE)) {
                gson.toJson(json, writer);
            }

            Wynnutils.LOGGER.info("Created default cycles file at " + CYCLES_FILE.getAbsolutePath());
        } catch (IOException e) {
            Wynnutils.LOGGER.error("Failed to create default cycles file", e);
            ErrorReporter.reportError("Failed to create default cycles file", e.getMessage() + ExceptionUtils.getStackTrace(e));
        }
    }

    public static String importCyclesFromChatTriggers() {
        File wynnutilsCycles = CYCLES_FILE;
        File chatTriggersModules = new File(Wynnutils.mc.runDirectory, "config/ChatTriggers/modules");

        if (!chatTriggersModules.exists() || !chatTriggersModules.isDirectory()) {
            return "ChatTriggers modules folder not found.";
        }

        File sourceFile = findCyclesFile(chatTriggersModules);

        if (sourceFile == null) {
            return "Could not find 'Cycles.json' or 'spells.json' in any ChatTriggers module.";
        }

        try {
            Files.copy(sourceFile.toPath(), wynnutilsCycles.toPath(), StandardCopyOption.REPLACE_EXISTING);
            loadCycles();
            return "Successfully imported cycles from " + sourceFile.getName();
        } catch (IOException e) {
            Wynnutils.LOGGER.error("Failed to import cycles", e);
            ErrorReporter.reportError("Failed to import cycles", e.getMessage() + ExceptionUtils.getStackTrace(e));
            return "Failed to import cycles: " + e.getMessage();
        }
    }

    private static File findCyclesFile(File directory) {
        File[] files = directory.listFiles();
        if (files == null) {
            return null;
        }

        for (File file : files) {
            if (file.isDirectory()) {
                File found = findCyclesFile(file);
                if (found != null) {
                    return found;
                }
            } else if (file.getName().equalsIgnoreCase("Cycles.json") || file.getName().equalsIgnoreCase("spells.json")) {
                return file;
            }
        }
        return null;
    }

    public static void addSpell() {
        try {
            if (System.currentTimeMillis() - lastSpell > 60000) {spellIndex = 0;}
            SpellmacroUtils.add(Arrays.asList(cycle[spellIndex]));
            spellIndex++;
            lastSpell = System.currentTimeMillis();
            if (spellIndex >= cycle.length) {
                spellIndex = 0;
            }
        } catch (Exception e) {
            ErrorReporter.reportError("Spellmacro addSpell failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
        }
    }
    public static String getCurrentCycle() {
        return currentCycle;
    }
    public static void setCycle(String cycleName) {
        try {
            if (cycles.containsKey(cycleName)) {
                currentCycle = cycleName;
                cycle = cycles.get(cycleName).cycle();
                cps = cycles.get(cycleName).cps();
                spellIndex = 0;

                ConfigManager.setSelectedCycle(cycleName);

                UChat.chat("Cycle set to " + cycleName);
            } else {
                UChat.chat("Cycle " + cycleName + " does not exist");
                UChat.chat("Available cycles: " + Arrays.toString(getCycleNames()));
            }
        } catch (Exception e) {
            ErrorReporter.reportError("Spellmacro setCycle failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
        }
    }

    public static String[] getCycleNames() {
        return cycles.keySet().toArray(new String[0]);
    }

    public static CycleConfig getCycleConfig(String cycleName) {
        return cycles.get(cycleName);
    }

    public static void saveCycles() {
        try (FileWriter writer = new FileWriter(CYCLES_FILE)) {
            Gson gson = new GsonBuilder().setPrettyPrinting().create();
            JsonObject json = new JsonObject();

            for (Map.Entry<String, CycleConfig> entry : cycles.entrySet()) {
                JsonObject cycleObj = new JsonObject();
                JsonArray cycleArray = new JsonArray();

                for (String[] spells : entry.getValue().cycle()) {
                    JsonArray spellArray = new JsonArray();
                    for (String spell : spells) {
                        spellArray.add(spell);
                    }
                    cycleArray.add(spellArray);
                }

                cycleObj.add("Cycle", cycleArray);
                cycleObj.addProperty("CPS", entry.getValue().cps());
                if (entry.getValue().cooldown() > 0) {
                    cycleObj.addProperty("Cooldown", entry.getValue().cooldown());
                }

                json.add(entry.getKey(), cycleObj);
            }

            gson.toJson(json, writer);
        } catch (IOException e) {
            Wynnutils.LOGGER.error("Failed to save cycles to " + CYCLES_FILE.getAbsolutePath(), e);
            ErrorReporter.reportError("Failed to save cycles to file", e.getMessage() + ExceptionUtils.getStackTrace(e));
        }
    }

    public static String addCycle(String cycleName, CycleConfig config) {
        try {
            if (cycles.containsKey(cycleName)) {
                return "Cycle with name " + cycleName + " already exists.";
            }
            cycles.put(cycleName, config);
            saveCycles();
            return "Added new cycle: " + cycleName;
        } catch (Exception e) {
            ErrorReporter.reportError("Spellmacro addCycle failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
            return "Failed to add cycle: " + e.getMessage();
        }
    }

    public static String deleteCycle(String cycleName) {
        try {
            if (!cycles.containsKey(cycleName)) {
                return "Cycle " + cycleName + " does not exist.";
            }
            if (currentCycle != null && currentCycle.equals(cycleName)) {
                currentCycle = null;
            }
            cycles.remove(cycleName);
            saveCycles();
            return "Deleted cycle: " + cycleName;
        } catch (Exception e) {
            ErrorReporter.reportError("Spellmacro deleteCycle failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
            return "Failed to delete cycle: " + e.getMessage();
        }
    }

    public static String renameCycle(String oldName, String newName) {
        try {
            if (!cycles.containsKey(oldName)) {
                return "Cycle " + oldName + " does not exist.";
            }
            if (cycles.containsKey(newName)) {
                return "A cycle with the name " + newName + " already exists.";
            }
            CycleConfig config = cycles.remove(oldName);
            cycles.put(newName, config);
            if (currentCycle != null && currentCycle.equals(oldName)) {
                currentCycle = newName;
                ConfigManager.setSelectedCycle(newName);
            }
            saveCycles();
            return "Renamed cycle " + oldName + " to " + newName;
        } catch (Exception e) {
            ErrorReporter.reportError("Spellmacro renameCycle failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
            return "Failed to rename cycle: " + e.getMessage();
        }
    }

    public static String editCycle(String cycleName, CycleConfig newConfig) {
        try {
            if (!cycles.containsKey(cycleName)) {
                return "Cycle " + cycleName + " does not exist.";
            }
            cycles.put(cycleName, newConfig);
            if (currentCycle != null && currentCycle.equals(cycleName)) {
                cycle = newConfig.cycle();
                cps = newConfig.cps();
                spellIndex = 0;
                cycle = cycles.get(currentCycle).cycle();
            }
            saveCycles();
            return "Edited cycle: " + cycleName;
        } catch (Exception e) {
            ErrorReporter.reportError("Spellmacro editCycle failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
            return "Failed to edit cycle: " + e.getMessage();
        }
    }

    public String getCurrentCycleName() {
        return currentCycle;
    }
    public record CycleConfig(String[][] cycle, int cps, int cooldown) {
    }

    private class PacketThread implements Runnable {
        @Override
        public void run() {
            while (true) {
                try {
                    if (SpellmacroUtils.size() > 0) {
                        String spell = SpellmacroUtils.get();
                        Packet<?> packet = SpellmacroUtils.getPacket(spell);
                        if (packet != null) {
                            PacketUtils.sendPacket(packet);
                        }
                    }
                    try {
                        Thread.sleep(1000 / cps);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                        ErrorReporter.reportError("Spellmacro thread interrupted", e.getMessage() + ExceptionUtils.getStackTrace(e));
                        break;
                    }
                } catch (Exception e) {
                    ErrorReporter.reportError("Spellmacro packet thread execution failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
                    try {
                        Thread.sleep(1000); // Wait before retrying
                    } catch (InterruptedException ie) {
                        break;
                    }
                }
            }
        }
    }
}
