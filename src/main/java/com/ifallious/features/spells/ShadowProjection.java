package com.ifallious.features.spells;

import com.ifallious.event.GlobalEventBus;
import com.ifallious.event.TickEvent;
import com.ifallious.utils.featureutils.SpellmacroUtils;
import com.ifallious.utils.minecraft.Tick;
import com.ifallious.utils.config.ConfigManager;
import com.ifallious.utils.ErrorReporter;
import com.ifallious.utils.wynntils.StatusEffect;
import com.ifallious.utils.wynntils.StatusEffectUtils;
import gg.essential.universal.UChat;
import gg.essential.universal.UKeyboard;
import gg.essential.universal.UMinecraft;
import gg.essential.universal.wrappers.UPlayer;
import meteordevelopment.orbit.EventHandler;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.awt.*;
import java.security.Key;
import java.util.Arrays;

public class ShadowProjection {
   public static StatusEffect effect;

    public ShadowProjection() {
        GlobalEventBus.subscribe(this);
    }
    @EventHandler
    public void onTick(TickEvent event) {
        try {
            if (Boolean.TRUE.equals(ConfigManager.getFeature("shadowProjection"))) {
                StatusEffect ShadowProjection = StatusEffectUtils.getStatusEffect("Shadow Projection");
                if (ShadowProjection == null) return;
                if (ShadowProjection.isIncluded()) {
                    if (ShadowProjection.getSeconds() == 0 && effect.getSeconds() != 0) {
                            Tick.schedule(10, () -> {
                                UMinecraft.getMinecraft().options.jumpKey.setPressed(true);
                            });
                            Tick.schedule(11, () -> {
                                UMinecraft.getMinecraft().options.jumpKey.setPressed(false);
                            });
                            Tick.schedule(12, () -> {
                                UMinecraft.getMinecraft().options.jumpKey.setPressed(true);
                            });
                            Tick.schedule(13, () -> {
                                UMinecraft.getMinecraft().options.jumpKey.setPressed(false);
                            });
                            Tick.schedule(22, () -> {
                                UMinecraft.getMinecraft().options.jumpKey.setPressed(true);
                            });
                            Tick.schedule(23, () -> {
                                UMinecraft.getMinecraft().options.jumpKey.setPressed(false);
                            });
                            Tick.schedule(24, () -> {
                                UMinecraft.getMinecraft().options.jumpKey.setPressed(true);
                            });
                            Tick.schedule(25, () -> {
                                UMinecraft.getMinecraft().options.jumpKey.setPressed(false);
                            });
                    }
                    effect = ShadowProjection;
                }
            }
        } catch (Exception e) {
            ErrorReporter.reportError("Shadow Projection tick event failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
        }
    }
}
