package com.ifallious.features.spells;

import com.ifallious.Wynnutils;
import com.ifallious.event.GlobalEventBus;
import com.ifallious.event.TickEvent;
import com.ifallious.event.TrapEvent;
import com.ifallious.utils.config.ConfigManager;
import gg.essential.universal.UMinecraft;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.entity.Entity;
import net.minecraft.entity.decoration.DisplayEntity;
import net.minecraft.world.World;

import java.util.Objects;

public class TrapTimer {
    public TrapTimer() {
        GlobalEventBus.subscribe(this);
    }
    @EventHandler
    public void onTick(TickEvent event) {
        try {
            if (Boolean.TRUE.equals(ConfigManager.getFeature("trapTimer"))) {
                World world = UMinecraft.getWorld();
                if (world != null) {
                    Iterable<Entity> entities = Objects.requireNonNull(UMinecraft.getWorld()).getEntities();
                    for (Entity entity : entities) {
                        try {
                            if (entity instanceof DisplayEntity.TextDisplayEntity textEntity) {
                                String text = textEntity.getText().getString();
                                if (text.contains("Arming")) {
                                    try {
                                        GlobalEventBus.post(new TrapEvent(entity.getBlockX(), entity.getBlockY(), entity.getBlockZ()));
                                    } catch (Exception e) {
                                        Wynnutils.LOGGER.error("TrapTimer event post failed", e);
                                    }
                                }
                            }
                        } catch (Exception e) {
                            Wynnutils.LOGGER.error("TrapTimer entity processing failed", e);
                        }
                    }
                }
            }
        } catch (Exception e) {
            Wynnutils.LOGGER.error("TrapTimer tick event failed", e);
        }
    }
}
