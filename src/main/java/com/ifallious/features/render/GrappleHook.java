package com.ifallious.features.render;

import com.ifallious.event.GlobalEventBus;
import com.ifallious.event.TickEvent;
import com.ifallious.utils.render.BlockRenderer;
import gg.essential.universal.UMinecraft;
import gg.essential.universal.wrappers.UPlayer;
import meteordevelopment.orbit.EventHandler;

public class GrappleHook {
    BlockRenderer renderer;
    public GrappleHook() {
        renderer = new BlockRenderer();
        GlobalEventBus.subscribe(this);
    }

    @EventHandler
    public void onTick(TickEvent e) {
        renderer.clearBlocks();
        renderer.addBlock();
    }
}
