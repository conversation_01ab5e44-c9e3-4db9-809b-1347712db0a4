package com.ifallious.features.war;
import com.ifallious.Wynnutils;
import com.ifallious.utils.config.ConfigManager;
import com.ifallious.utils.ErrorReporter;
import com.ifallious.utils.wynntils.WarUtils;
import com.ifallious.utils.minecraft.AtrributeUtils;
import net.fabricmc.fabric.api.client.event.lifecycle.v1.ClientTickEvents;
import net.minecraft.entity.attribute.EntityAttributes;
import org.apache.commons.lang3.exception.ExceptionUtils;

public class AuraDodge {
    public AuraDodge() {
        ClientTickEvents.END_CLIENT_TICK.register(client -> {
            try {
                if (WarUtils.isInWar() && Boolean.TRUE.equals(ConfigManager.getFeature("jumpHeight"))) {
                    try {
                        AtrributeUtils.addAttribute("jump_strength", EntityAttributes.JUMP_STRENGTH, 0.39f);
                    } catch (Exception e) {
                        ErrorReporter.reportError("AuraDodge attribute addition failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
                    }
                } else {
                    try {
                        AtrributeUtils.resetAttribute("jump_strength", EntityAttributes.JUMP_STRENGTH);
                    } catch (Exception e) {
                        ErrorReporter.reportError("AuraDodge attribute reset failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
                    }
                }
                if (WarUtils.getAuraTimer() < 600 && WarUtils.getAuraTimer() > 550 && ConfigManager.getFeature("auraDodge")) {
                    try {
                        Wynnutils.mc.player.jump();
                    } catch (Exception e) {
                        ErrorReporter.reportError("AuraDodge player jump failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
                    }
                }
            } catch (Exception e) {
                ErrorReporter.reportError("AuraDodge tick event failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
            }
        });
    }
}
