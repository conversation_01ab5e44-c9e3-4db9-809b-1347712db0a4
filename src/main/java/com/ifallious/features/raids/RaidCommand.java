package com.ifallious.features.raids;

import java.util.ArrayList;
import java.util.List;

import com.ifallious.event.GlobalEventBus;
import com.ifallious.event.PacketEvent;
import com.ifallious.utils.Utils;

import com.ifallious.utils.minecraft.Tick;
import gg.essential.universal.UChat;
import gg.essential.universal.UMinecraft;
import gg.essential.universal.wrappers.UPlayer;
import meteordevelopment.orbit.EventHandler;
import net.fabricmc.fabric.api.client.command.v2.ClientCommandManager;
import net.fabricmc.fabric.api.client.command.v2.ClientCommandRegistrationCallback;
import net.minecraft.item.ItemStack;
import net.minecraft.network.packet.s2c.play.OpenScreenS2CPacket;
import net.minecraft.screen.slot.SlotActionType;

public class RaidCommand {
    public ClickSequence clickSequence;
    public RaidCommand() {
        GlobalEventBus.subscribe(this);
        ClientCommandRegistrationCallback.EVENT.register((dispatcher, registryAccess) -> {
            dispatcher.register(ClientCommandManager.literal("tcc").executes(context -> {
                ArrayList<ClickAction> actions = new ArrayList<>();
                actions.add(new ClickAction("Party Finder", "Party Queue"));
                actions.add(new ClickAction("Party Finder", "The Canyon Colossus"));
                actions.add(new ClickAction("", "Ready Up!"));
                clickSequence = new ClickSequence("tcc", actions);
                Utils.sendCommand("pfinder");
                return 1;
            }));
            dispatcher.register(ClientCommandManager.literal("tna").executes(context -> {
                ArrayList<ClickAction> actions = new ArrayList<>();
                actions.add(new ClickAction("Party Finder", "Party Queue"));
                actions.add(new ClickAction("Party Finder", "The Nameless Anomaly"));
                actions.add(new ClickAction("", "Ready Up!"));
                clickSequence = new ClickSequence("tna", actions);
                Utils.sendCommand("pfinder");
                return 1;
            }));
            dispatcher.register(ClientCommandManager.literal("nol").executes(context -> {
                ArrayList<ClickAction> actions = new ArrayList<>();
                actions.add(new ClickAction("Party Finder", "Party Queue"));
                actions.add(new ClickAction("Party Finder", "Orphion's Nexus of Light"));
                actions.add(new ClickAction("", "Ready Up!"));
                clickSequence = new ClickSequence("nol", actions);
                Utils.sendCommand("pfinder");
                return 1;
            }));
            dispatcher.register(ClientCommandManager.literal("notg").executes(context -> {
                ArrayList<ClickAction> actions = new ArrayList<>();
                actions.add(new ClickAction("Party Finder", "Party Queue"));
                actions.add(new ClickAction("Party Finder", "Nest of the Grootslangs"));
                actions.add(new ClickAction("", "Ready Up!"));
                clickSequence = new ClickSequence("notg", actions);
                Utils.sendCommand("pfinder");
                return 1;
            }));
        });
    }

    @EventHandler
    public void onPacket(PacketEvent.PacketReceiveEvent e) {
        if (e.packet instanceof OpenScreenS2CPacket packet) {
            if (clickSequence == null || clickSequence.isEmpty()) return;
            ClickAction action = clickSequence.getAction();
            if (packet.getName().getString().contains(action.InventoryTitle)) {
                processAction(action);
            }
        }
    }
    private void processAction(ClickAction action) {
        if (UMinecraft.getPlayer() == null) return;
        Tick.schedule((long) (Math.ceil(UPlayer.getPlayer().networkHandler.getPlayerListEntry(UPlayer.getUUID()).getLatency() / 50) + 1), () -> {
            int foundIndex = -1;
            for (int i = 0; i < UMinecraft.getPlayer().currentScreenHandler.slots.size(); i++) {
                ItemStack item = UMinecraft.getPlayer().currentScreenHandler.slots.get(i).getStack();
                String itemName = item.getName().getString();
                if (itemName.contains(action.ClickName)) {
                    foundIndex = i;
                    break;
                }
            }
            if (foundIndex != -1) {
                UMinecraft.getMinecraft().interactionManager.clickSlot(UMinecraft.getPlayer().currentScreenHandler.syncId, foundIndex, 0, SlotActionType.PICKUP, UMinecraft.getPlayer());
            }
        });
    }
    private class ClickAction {
        public String InventoryTitle;
        public String ClickName;
        public Boolean CheckItem;
        public int CheckSlot;
        public ClickAction(String InventoryTitle, String ClickName) {
            this.InventoryTitle = InventoryTitle;
            this.ClickName = ClickName;
            this.CheckItem = false;
            this.CheckSlot = -1;
        }
        public ClickAction(String InventoryTitle, String ClickName, Boolean CheckItem, int CheckSlot) {
            this.InventoryTitle = InventoryTitle;
            this.ClickName = ClickName;
            this.CheckItem = CheckItem;
            this.CheckSlot = CheckSlot;
        }
    }

    private class ClickSequence {
        public String Name;
        public List<ClickAction> Actions;
        public ClickSequence(String Name, List<ClickAction> Actions) {
            this.Name = Name;
            this.Actions = Actions;
        }
        public ClickAction getAction() {
            ClickAction action = Actions.get(0);
            Actions.remove(action);
            return action;
        }
        public Boolean isEmpty() {
            return Actions.isEmpty();
        }
    }
}

