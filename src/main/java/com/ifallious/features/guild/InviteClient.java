package com.ifallious.features.guild;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ifallious.utils.Utils;
import com.wynntils.core.components.Models;
import gg.essential.universal.UChat;
import gg.essential.universal.UMinecraft;
import gg.essential.universal.wrappers.UPlayer;
import okhttp3.*;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.*;

public class InviteClient {
    private static final String INVITE_URL =
            System.getenv().getOrDefault("INVITE_URL", "ws://141.147.3.12:8787");
    private static final String CLIENT_ID =
            System.getenv().getOrDefault("CLIENT_ID", "java-client-1");
    private static final String LABEL =
            System.getenv().getOrDefault("LABEL", "Java Client 1");

    private final OkHttpClient http = new OkHttpClient.Builder()
            .retryOnConnectionFailure(true)
            .build();

    private final ObjectMapper mapper = new ObjectMapper();
    private final ScheduledExecutorService scheduler =
            Executors.newSingleThreadScheduledExecutor();

    private volatile WebSocket socket;


    public void start() {
        connect();
    }

    private void connect() {
        System.out.println("Connecting to " + INVITE_URL + " ...");
        Request req = new Request.Builder().url(INVITE_URL).build();
        socket = http.newWebSocket(req, new WSListener());
    }

    private void scheduleReconnect() {
        System.out.println("Reconnecting in 2s...");
        scheduler.schedule(this::connect, 2, TimeUnit.SECONDS);
    }

    private void sendRegister(WebSocket ws) {
        Map<String, Object> reg = new HashMap<>();
        reg.put("type", "register");
        reg.put("clientId", CLIENT_ID);
        reg.put("label", LABEL);
        reg.put("capabilities", List.of("invite"));
        try {
            ws.send(mapper.writeValueAsString(reg));
            System.out.println("Sent register payload.");
        } catch (Exception e) {
            System.out.println("Failed to send register: " + e.getMessage());
        }
    }

    private void sendInviteResult(WebSocket ws, String requestId, boolean ok, String error) {
        Map<String, Object> res = new HashMap<>();
        res.put("type", "invite_result");
        res.put("requestId", requestId);
        res.put("status", ok ? "success" : "failure");
        if (!ok && error != null && !error.isBlank()) {
            res.put("error", error);
        }
        try {
            ws.send(toJson(res));
        } catch (Exception e) {
            System.out.println("Failed to send invite_result: " + e.getMessage());
        }
    }

    private String toJson(Object o) throws IOException {
        return mapper.writeValueAsString(o);
    }

    private boolean performInvite(String username, String guild) throws InterruptedException {
        UChat.chat("Inviting: " + username);
        if (!UPlayer.getPlayer().networkHandler.getServerInfo().address.toLowerCase().contains("wynncraft")) return false;
        if (!Models.Guild.getGuildName().equals("KongoBoys")) return false;
        boolean ok = false;
        try {
            Utils.sendCommand("gu invite " + username);
            ok = true;
        } catch (Exception e) {

        }
        return ok;
    }

    private class WSListener extends WebSocketListener {
        @Override
        public void onOpen(WebSocket ws, Response response) {
            System.out.println("Connected.");
            sendRegister(ws);
        }

        @Override
        public void onMessage(WebSocket ws, String text) {
            Map<String, Object> msg;
            try {
                msg = mapper.readValue(text, new TypeReference<Map<String, Object>>() {});
            } catch (Exception e) {
                return;
            }
            if (msg == null || !(msg.get("type") instanceof String)) return;

            String type = (String) msg.get("type");
            if ("invite".equals(type)) {
                String requestId = (String) msg.get("requestId");
                String username = (String) msg.get("username");
                String guild = (String) msg.getOrDefault("guild", "DUDE");

                if (requestId == null || username == null) return;

                try {
                    boolean ok = performInvite(username, guild);
                    sendInviteResult(ws, requestId, ok, ok ? null : "invite_failed");
                    System.out.printf("[invite] %s: %s%n", ok ? "Success" : "Failure", username);
                } catch (Exception e) {
                    sendInviteResult(ws, requestId, false, e.getMessage());
                    System.out.printf("[invite] Failure: %s (%s)%n", username, e.getMessage());
                }
            }
        }

        @Override
        public void onClosing(WebSocket ws, int code, String reason) {
            System.out.println("Closing: " + code + " " + reason);
            ws.close(1000, null);
        }

        @Override
        public void onClosed(WebSocket ws, int code, String reason) {
            System.out.println("Closed: " + code + " " + reason);
            scheduleReconnect();
        }

        @Override
        public void onFailure(WebSocket ws, Throwable t, Response response) {
            System.out.println("Socket error: " + (t.getMessage() != null ? t.getMessage() : t));
            scheduleReconnect();
        }
    }
}
