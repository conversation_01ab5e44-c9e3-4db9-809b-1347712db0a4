package com.ifallious.features.guild;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.ifallious.event.GlobalEventBus;
import com.ifallious.utils.ErrorReporter;
import com.ifallious.utils.Utils;
import com.ifallious.utils.config.ConfigManager;
import com.ifallious.utils.featureutils.SpellmacroUtils;
import com.mojang.brigadier.arguments.StringArgumentType;
import gg.essential.universal.UChat;
import net.fabricmc.fabric.api.client.command.v2.ClientCommandManager;
import net.fabricmc.fabric.api.client.command.v2.ClientCommandRegistrationCallback;
import net.fabricmc.fabric.api.client.event.lifecycle.v1.ClientTickEvents;
import net.fabricmc.fabric.api.client.keybinding.v1.KeyBindingHelper;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.client.util.InputUtil;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.lwjgl.glfw.GLFW;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

public class GuildRecruiter {
    private static final Gson GSON = new Gson();
    private static final String API_URL = "https://guildchecker.vercel.app/api/no-guild-players?min_level=";
    static List<String> players = new ArrayList<>();
    private static KeyBinding messageKey;
    
    public static void registerCommand() {
        ClientCommandRegistrationCallback.EVENT.register((dispatcher, registryAccess) -> {
            dispatcher.register(ClientCommandManager.literal("guildrecruit").executes(context -> {
                UChat.chat("Fetching Recruitable Members. This may take up to 5 Minutes.");
                requestNoGuildPlayers();
                return 1;
            }));
            dispatcher.register(ClientCommandManager.literal("exclude").then(ClientCommandManager.argument("Username", StringArgumentType.string()).executes( context -> {
                excludePlayer(StringArgumentType.getString(context, "Username"), " ");
                        return 1;
                }
            ).then(ClientCommandManager.argument("Reason", StringArgumentType.greedyString()).executes(context -> {
                excludePlayer(StringArgumentType.getString(context, "Username"), StringArgumentType.getString(context, "Reason"));
                return 1;
                }))
            ));
        });
        messageKey = KeyBindingHelper.registerKeyBinding(new KeyBinding("Recruitment Message", InputUtil.Type.KEYSYM, GLFW.GLFW_KEY_UNKNOWN, "Wynnutils"));
        ClientTickEvents.END_CLIENT_TICK.register(client -> {
            if (messageKey.wasPressed()) {
                if (players.size() > 0) {
                    String name = players.get(0);
                    Utils.sendCommand("msg " + name + " " + ConfigManager.getSetting("recruitingMessage"));
                    players.remove(0);
                    UChat.chat("Messaged: " + name + " " + players.size() + " Players left");
                } else {
                    UChat.chat("No Players Found, type /guildrecruit first");
                }
            }
        });
    }

    private static void excludePlayer(String username, String reason) {
        OkHttpClient client = new OkHttpClient();
        Request request = new Request.Builder().url("https://guildchecker.vercel.app/api/blacklist/add?player="+username+"&reason="+reason).build();
        try (Response response = client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                UChat.chat("Failed to exclude Player: " + username);
                return;
            }
            UChat.chat("Excluded Player: " + username);
        } catch (IOException e) {
            e.printStackTrace();
        }

    }
    
    public static void requestNoGuildPlayers() {
        new Thread(() -> {
            try {
                OkHttpClient client = new OkHttpClient.Builder()
                    .connectTimeout(5, TimeUnit.MINUTES)
                    .readTimeout(5, TimeUnit.MINUTES)
                    .writeTimeout(5, TimeUnit.MINUTES)
                    .build();

                Request request = new Request.Builder()
                    .url(API_URL+ ConfigManager.getSetting("recruitingLevel"))
                    .build();

                try (Response response = client.newCall(request).execute()) {
                    if (!response.isSuccessful()) {
                        UChat.chat("§c§l[GuildRecruiter] §7Failed to fetch data: HTTP " + response.code());
                        return;
                    }
                    
                    String responseBody = response.body().string();
                    processResponse(responseBody);
                    
                } catch (IOException e) {
                    UChat.chat("§c§l[GuildRecruiter] §7Failed to fetch data: " + e.getMessage());
                }
                
            } catch (Exception e) {
                UChat.chat("§c§l[GuildRecruiter] §7Error: " + e.getMessage());
            }
        }).start();
    }
    
    private static void processResponse(String responseBody) {
        try {
            JsonObject jsonResponse = JsonParser.parseString(responseBody).getAsJsonObject();

            int totalOnlinePlayers = jsonResponse.has("total_online_players") ? 
                jsonResponse.get("total_online_players").getAsInt() : 0;
            int checkedPlayers = jsonResponse.has("checked_players") ? 
                jsonResponse.get("checked_players").getAsInt() : 0;
            double onlinePlayersProcessedPercent = jsonResponse.has("online_players_processed_percent") ? 
                jsonResponse.get("online_players_processed_percent").getAsDouble() : 0.0;

            if (jsonResponse.has("players") && jsonResponse.get("players").isJsonArray()) {
                players.clear();
                JsonArray playersArray = jsonResponse.getAsJsonArray("players");
                for (int i = 0; i < playersArray.size(); i++) {
                    players.add(playersArray.get(i).getAsJsonObject().get("username").getAsString());
                }
            }
            UChat.chat("§6§l[GuildRecruiter] §7API Response Statistics:\n" + "§eTotal Online Players: §a" + totalOnlinePlayers + "\n§eChecked Players: §a" + checkedPlayers + "\n§eOnline Players Processed: §a" + String.format("%.2f", onlinePlayersProcessedPercent) + "%" + "\n§ePlayers without Guild: §a" + players.size());
        } catch (Exception e) {
            UChat.chat("§c§l[GuildRecruiter] §7Failed to parse response: " + e.getMessage());
        }
    }
}
