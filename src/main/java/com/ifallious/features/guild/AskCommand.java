package com.ifallious.features.guild;

import com.ifallious.event.GlobalEventBus;
import com.ifallious.event.GuildChatMessageEvent;
import com.ifallious.utils.Utils;
import com.ifallious.utils.ai.ConversationManager;
import com.ifallious.utils.config.ConfigManager;
import gg.essential.universal.UChat;
import gg.essential.universal.wrappers.UPlayer;
import meteordevelopment.orbit.EventHandler;

import java.util.HashMap;
import java.util.Objects;

public class AskCommand {
    static HashMap<String, ConversationManager> conversations = new HashMap<>();
    public AskCommand() {
        GlobalEventBus.subscribe(this);
    }

    @EventHandler
    public void onGuildChatMessage(GuildChatMessageEvent event) {
        if (!ConfigManager.getFeature("guildResponse") || Objects.equals(event.getUsername(), UPlayer.getPlayer().getNameForScoreboard())) return;
        ConversationManager conversation = conversations.containsKey(event.getUsername()) ? conversations.get(event.getUsername()) : ConversationManager.create(100).system("You are talking to a user named: " + event.getUsername()+ ", You have access to tools. Use the available tools when appropriate to answer questions.");
        if (event.getMessage().contains("!ask")) {
            conversation.sendMessage(event.getMessage()).thenAccept(response -> {
                // Filter response to only include Minecraft-compatible ASCII characters
                String filteredResponse = response.replaceAll("[^\\x20-\\x7E]", " ");
                //add guild send logic
                Utils.sendCommand("g " + filteredResponse);
                conversations.put(event.getUsername(), conversation);
            });
        } else {
            conversation.addUserMessage(event.getMessage());
            conversations.put(event.getUsername(), conversation);
        }
    }

    public static void clearConversations() {
        conversations.clear();
    }
}
