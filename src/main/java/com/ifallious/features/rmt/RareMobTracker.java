package com.ifallious.features.rmt;

import com.ifallious.utils.ErrorReporter;
import com.ifallious.utils.Utils;
import com.ifallious.utils.config.ConfigManager;
import gg.essential.universal.UMinecraft;
import gg.essential.universal.wrappers.UPlayer;
import net.minecraft.entity.Entity;
import net.minecraft.entity.decoration.DisplayEntity.TextDisplayEntity;
import net.minecraft.sound.SoundCategory;
import net.minecraft.sound.SoundEvents;
import net.minecraft.world.World;

import java.util.Objects;

import org.apache.commons.lang3.exception.ExceptionUtils;

public class RareMobTracker {
    public RareMobTracker() {
        try {
            Thread thread = new Thread(new RmtThread());
            thread.start();
        } catch (Exception e) {
            ErrorReporter.reportError("RareMobTracker thread creation failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
        }
    }
    private class RmtThread implements Runnable {
        private long lastMessage = 0;
        @Override
        public void run() {
            while (true) {
                try {
                    World world = UMinecraft.getWorld();
                    if (world == null) {
                        Thread.sleep(2000);
                        continue;
                    }
                    Iterable<Entity> entities = Objects.requireNonNull(UMinecraft.getWorld()).getEntities();
                    for (Entity entity : entities) {
                        try {
                            if (entity instanceof TextDisplayEntity textEntity) {
                                String text = textEntity.getText().getString();
                                if (text.contains("\uE02A") && Boolean.TRUE.equals(ConfigManager.getFeature("rareMobNotification")) && System.currentTimeMillis() - lastMessage > 60000) {
                                    try {
                                        Utils.simulateChat("§7§lRare Mob: " + cleanString(text).trim() + " found at: [" + entity.getBlockX() + " " + entity.getBlockY() + " " + entity.getBlockZ() + "]");
                                        UPlayer.getPlayer().playSoundToPlayer(SoundEvents.BLOCK_AMETHYST_BLOCK_BREAK, SoundCategory.MASTER, 1.0f, 1.0f);
                                    } catch (Exception e) {
                                        ErrorReporter.reportError("RareMobTracker notification failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
                                    }
                                    Thread.sleep(60000);
                                    break;
                                }
                            }
                        } catch (Exception e) {
                            ErrorReporter.reportError("RareMobTracker entity processing failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
                        }
                    }
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                    ErrorReporter.reportError("RareMobTracker thread interrupted", e.getMessage() + ExceptionUtils.getStackTrace(e));
                    break;
                } catch (Exception e) {
                    ErrorReporter.reportError("RareMobTracker thread execution failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
                    try {
                        Thread.sleep(5000); // Wait before retrying
                    } catch (InterruptedException ie) {
                        break;
                    }
                }
            }
        }
    }
    private String cleanString(String input) {
        try {
            return input.replaceAll("[^\\p{ASCII}]", "");
        } catch (Exception e) {
            ErrorReporter.reportError("RareMobTracker string cleaning failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
            return input != null ? input : "";
        }
    }
}
