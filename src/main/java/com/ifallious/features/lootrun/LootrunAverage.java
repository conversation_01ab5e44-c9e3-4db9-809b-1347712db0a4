package com.ifallious.features.lootrun;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.ifallious.utils.ErrorReporter;
import gg.essential.universal.UChat;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;

import org.apache.commons.lang3.exception.ExceptionUtils;

public class LootrunAverage {
    private static final HttpClient client = HttpClient.newBuilder()
            .connectTimeout(Duration.ofSeconds(10))
            .build();
    
    public static void getLootrunAvg() {
        try {
            UChat.chat("§e§l[Wynnutils] §7Requesting lootrun data...");
            
            HttpRequest request = HttpRequest.newBuilder().uri(URI.create("https://guildchecker.vercel.app/api/region-mythic-prices")).GET().build();
            
            client.sendAsync(request, HttpResponse.BodyHandlers.ofString()).thenApply(HttpResponse::body).thenAccept(LootrunAverage::handleResponse).exceptionally(e -> {
                try {
                    UChat.chat("§c§l[Wynnutils] §7Failed to fetch lootrun data: " + e.getMessage());
                    if (e instanceof Exception) {
                        ErrorReporter.reportError("Failed to fetch lootrun data", (Exception) e + e.getStackTrace().toString());
                    }
                } catch (Exception ex) {
                    ErrorReporter.reportError("LootrunAverage error handling failed", ex.getMessage() + ExceptionUtils.getStackTrace(ex));
                }
                return null;
            });
        } catch (Exception e) {
            ErrorReporter.reportError("LootrunAverage request creation failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
        }
    }
    
    private static void handleResponse(String responseBody) {
        try {
            JsonObject jsonResponse = JsonParser.parseString(responseBody).getAsJsonObject();
            JsonObject regionPrices = jsonResponse.getAsJsonObject("region_prices");
            
            UChat.chat("§6§lAverage Mythic prices for Lootrun Camps (in thousands of emeralds):");
            UChat.chat("§b§lSilent Expanse: §a" + regionPrices.getAsJsonObject("SE").get("average_price").getAsString());
            UChat.chat("§3§lSky Islands: §a" + regionPrices.getAsJsonObject("Sky").get("average_price").getAsString());
            UChat.chat("§c§lMolten Heights: §a" + regionPrices.getAsJsonObject("Molten").get("average_price").getAsString());
            UChat.chat("§e§lCanyon of the Lost: §a" + regionPrices.getAsJsonObject("Canyon").get("average_price").getAsString());
            UChat.chat("§2§lCorkus: §a" + regionPrices.getAsJsonObject("Corkus").get("average_price").getAsString());
        } catch (Exception e) {
            UChat.chat("§c§l[Wynnutils] §7Error parsing lootrun data: " + e.getMessage());
            ErrorReporter.reportError("Error parsing lootrun data", e.getMessage() + ExceptionUtils.getStackTrace(e));
        }
    }
}