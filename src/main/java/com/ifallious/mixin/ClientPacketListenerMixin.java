package com.ifallious.mixin;

import com.ifallious.Wynnutils;
import com.ifallious.event.GlobalEventBus;
import com.wynntils.mc.event.ChatPacketReceivedEvent;
import gg.essential.universal.UChat;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.network.ClientConnectionState;
import net.minecraft.client.network.ClientPlayNetworkHandler;
import net.minecraft.client.network.ClientCommonNetworkHandler;
import net.minecraft.network.ClientConnection;
import net.minecraft.network.packet.s2c.play.GameMessageS2CPacket;
import net.minecraft.text.Text;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Unique;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

@Mixin(ClientPlayNetworkHandler.class)
public abstract class ClientPacketListenerMixin extends ClientCommonNetworkHandler {
    protected ClientPacketListenerMixin(MinecraftClient client, ClientConnection connection, ClientConnectionState connectionState) {
        super(client, connection, connectionState);
    }
    @Unique
    private static boolean isRenderThread() {
        return Wynnutils.mc.isOnThread();
    }

    @Inject(
            method = "onGameMessage(Lnet/minecraft/network/packet/s2c/play/GameMessageS2CPacket;)V",
            at =
            @At(
                    value = "INVOKE",
                    target =
                            "Lnet/minecraft/client/network/message/MessageHandler;onGameMessage(Lnet/minecraft/text/Text;Z)V"),
            cancellable = true)
    private void onGameMessage(GameMessageS2CPacket packet, CallbackInfo ci) {
        if (!isRenderThread()) return;
        Text message = packet.content();
        ChatPacketReceivedEvent event = packet.overlay()
                ? new ChatPacketReceivedEvent.GameInfo(message)
                : new ChatPacketReceivedEvent.System(message);
        try {
            GlobalEventBus.post(event);
        } catch (Exception ignored) {
            ignored.printStackTrace();
        }

    }
}
