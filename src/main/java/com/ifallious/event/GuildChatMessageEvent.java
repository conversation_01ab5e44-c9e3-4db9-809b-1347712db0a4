package com.ifallious.event;

import gg.essential.universal.UChat;

/**
 * Event fired when a full guild chat message has been aggregated and parsed.
 */
public class GuildChatMessageEvent {
    private final String username;
    private final String message;

    public GuildChatMessageEvent(String username, String message) {
        this.username = username;
        this.message = message;
    }

    public String getUsername() {
        return username;
    }

    public String getMessage() {
        return message;
    }
}

