package com.ifallious.event;

import net.minecraft.particle.ParticleEffect;

public class ParticleEvent {
    public final double x;
    public final double y;
    public final double z;
    public final float offsetX;
    public final float offsetY;
    public final float offsetZ;
    public final float speed;
    public final int count;
    public final boolean forceSpawn;
    public final boolean important;
    public final ParticleEffect parameters;

    public ParticleEvent(double x, double y, double z, float offsetX, float offsetY, float offsetZ, float speed, int count, boolean forceSpawn, boolean important, ParticleEffect parameters) {
        this.x = x;
        this.y = y;
        this.z = z;
        this.offsetX = offsetX;
        this.offsetY = offsetY;
        this.offsetZ = offsetZ;
        this.speed = speed;
        this.count = count;
        this.forceSpawn = forceSpawn;
        this.important = important;
        this.parameters = parameters;
    }
}
