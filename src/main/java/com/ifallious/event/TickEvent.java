package com.ifallious.event;

/**
 * Event that is fired every game tick
 */
public class TickEvent {
    private final long tick;

    /**
     * Create a new tick event
     * @param currenttick The current tick count
     */
    public TickEvent(long currenttick) {
        this.tick = currenttick;
    }

    /**
     * Get the current tick count
     * @return The current tick count
     */
    public long getCurrentTick() {
        return tick;
    }
}
