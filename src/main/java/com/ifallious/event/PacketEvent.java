package com.ifallious.event;

import net.minecraft.network.ClientConnection;
import net.minecraft.network.packet.Packet;

public class PacketEvent {
    public static class PacketReceiveEvent extends Cancellable {
        public Packet<?> packet;
        public ClientConnection connection;

        public PacketReceiveEvent(Packet<?> packet, ClientConnection connection) {
            this.setCancelled(false);
            this.packet = packet;
            this.connection = connection;
        }
    }
}

