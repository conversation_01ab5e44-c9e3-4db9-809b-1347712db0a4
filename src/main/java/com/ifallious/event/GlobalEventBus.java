package com.ifallious.event;

import java.lang.invoke.MethodHandles;
import meteordevelopment.orbit.EventBus;
import meteordevelopment.orbit.IEventBus;

/**
 * A global event bus for the mod to handle events across different components
 */
public class GlobalEventBus {
    private static final IEventBus BUS = new EventBus();
    static {
        BUS.registerLambdaFactory("com.ifallious", (lookupInMethod, klass) -> (MethodHandles.Lookup) lookupInMethod.invoke(null, klass, MethodHandles.lookup()));
    }
    /**
     * Subscribe an object to receive events from the event bus
     * @param listener The object to subscribe
     */
    public static void subscribe(Object listener) {
        BUS.subscribe(listener);
    }

    /**
     * Unsubscribe an object from the event bus
     * @param listener The object to unsubscribe
     */
    public static void unsubscribe(Object listener) {
        BUS.unsubscribe(listener);
    }

    /**
     * Post an event to the event bus
     * @param event The event to post
     * @param <E> The type of the event
     */
    public static <E> void post(E event) {
        BUS.post(event);
    }

    /**
     * Get the event bus instance
     * @return The event bus
     */
    public static IEventBus getBus() {
        return BUS;
    }
}
