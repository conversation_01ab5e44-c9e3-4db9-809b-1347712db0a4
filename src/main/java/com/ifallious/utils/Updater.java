package com.ifallious.utils;

import com.ifallious.Wynnutils;
import com.ifallious.event.GlobalEventBus;
import com.ifallious.event.TickEvent;
import com.wynntils.core.components.Models;
import com.wynntils.models.worlds.WorldStateModel;
import gg.essential.universal.UChat;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.*;
import java.util.Scanner;

import meteordevelopment.orbit.EventHandler;
import net.neoforged.bus.api.SubscribeEvent;
import com.wynntils.models.worlds.event.WorldStateEvent;

import org.apache.commons.lang3.exception.ExceptionUtils;

public class Updater {
    private static final String UPDATE_INFO_URL = "https://wynnutils.vercel.app/api/version"; // Replace with your endpoint
    private static File getModsFolder() {
        return new File(Wynnutils.mc.runDirectory, "mods");
    }
    private static final String TEMP_UPDATE_JAR = "wynnutils-latest.jar.tmp";
    private static final String MOD_JAR_PREFIX = "wynnutils";
    private static final String MOD_JAR_EXTENSION = ".jar";
    private boolean firstJoin = true;

    public Updater() {
        GlobalEventBus.subscribe(this);
    }
    @EventHandler
    public void onTick(TickEvent event) {
        if (Models.WorldState.onWorld() && firstJoin) {
            Wynnutils.LOGGER.info("Updater: Checking for updates on first join");
            checkForUpdates();
            firstJoin = false;
        }
    }
    public static void checkForUpdates() {
        try {
            // 1. Fetch latest version info
            String json = fetchUrl(UPDATE_INFO_URL);
            String latestVersion = parseJson(json, "version");
            String downloadUrl = "https://wynnutils.vercel.app/api/download";

            if (isNewerVersion(latestVersion, Wynnutils.VERSION)) {
                UChat.chat("§5§l[Wynnutils] §fA new version (§a" + latestVersion + "§f) is available! §eDownloading update...");
                File tempFile = new File(getModsFolder(), TEMP_UPDATE_JAR);
                downloadFile(downloadUrl, tempFile);
                // Find the current mod jar
                File modsDir = getModsFolder();
                File[] modJars = modsDir.listFiles((dir, name) -> name.startsWith(MOD_JAR_PREFIX) && name.endsWith(MOD_JAR_EXTENSION) && !name.endsWith("-sources.jar"));
                if (modJars != null && modJars.length > 0) {
                    File currentJar = modJars[0];
                    addShutdownHook(currentJar, tempFile);
                    UChat.chat("§5§l[Wynnutils] §aUpdate downloaded. It will be installed on next restart.");
                    Wynnutils.LOGGER.info("Updater: Shutdown hook registered to install update.");
                }
            } else {
                Wynnutils.LOGGER.info("Updater: Wynnutils is up to date.");
                //UChat.chat("Wynnutils is up to date.");
            }
        } catch (Exception e) {
            Wynnutils.LOGGER.error("Updater: An error occurred during the update check.", e);
            ErrorReporter.reportError("Updater error during update check", e.getMessage() + ExceptionUtils.getStackTrace(e));
        }
    }

    private static String fetchUrl(String urlString) throws IOException {
        HttpURLConnection conn = (HttpURLConnection) new URL(urlString).openConnection();
        conn.setRequestMethod("GET");
        conn.setConnectTimeout(5000);
        conn.setReadTimeout(5000);
        conn.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36");
        try (InputStream in = conn.getInputStream(); Scanner scanner = new Scanner(in)) {
            scanner.useDelimiter("\\A");
            return scanner.hasNext() ? scanner.next() : "";
        }
    }

    private static void downloadFile(String urlString, File dest) throws IOException {
        String currentUrl = urlString;
        for (int i = 0; i < 10; i++) { // Limit redirects to 10 to prevent infinite loops
            Wynnutils.LOGGER.info("Updater: Attempting to connect to " + currentUrl);
            HttpURLConnection conn = (HttpURLConnection) new URL(currentUrl).openConnection();
            conn.setInstanceFollowRedirects(false); // We are handling redirects manually
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(15000);
            conn.setReadTimeout(15000);
            conn.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36");

            int status = conn.getResponseCode();
            Wynnutils.LOGGER.info("Updater: Received HTTP status " + status + " from " + currentUrl);

            if (status >= 300 && status <= 399) { // Handle redirect
                String newUrl = conn.getHeaderField("Location");
                if (newUrl == null) {
                    throw new IOException("Redirect requested but no Location header found.");
                }
                Wynnutils.LOGGER.info("Updater: Redirecting to " + newUrl);
                currentUrl = newUrl;
                conn.disconnect();
                continue; // Try next URL
            }

            if (status == HttpURLConnection.HTTP_OK) {
                try (InputStream in = conn.getInputStream(); FileOutputStream out = new FileOutputStream(dest)) {
                    byte[] buffer = new byte[8192];
                    int bytesRead;
                    long totalBytesRead = 0;
                    Wynnutils.LOGGER.info("Updater: Starting download to " + dest.getAbsolutePath());
                    while ((bytesRead = in.read(buffer)) != -1) {
                        out.write(buffer, 0, bytesRead);
                        totalBytesRead += bytesRead;
                    }
                    Wynnutils.LOGGER.info("Updater: Download complete. Total bytes: " + totalBytesRead);
                    return; // Success
                }
            } else {
                 try (InputStream errorStream = conn.getErrorStream();
                     Scanner scanner = new Scanner(errorStream).useDelimiter("\\A")) {
                    String errorBody = scanner.hasNext() ? scanner.next() : "No error body.";
                    Wynnutils.LOGGER.error("Updater: Server returned non-OK status: " + status + ". Body: " + errorBody);
                    ErrorReporter.reportError("Updater server error", "Server returned status " + status + ". Body: " + errorBody);
                }
                throw new IOException("Server returned non-OK status: " + status);
            }
        }
        throw new IOException("Too many redirects.");
    }

    // Simple JSON parser for flat key-value pairs
    private static String parseJson(String json, String key) {
        String pattern = "\\\"" + key + "\\\"\\s*:\\s*\\\"([^\\\"]+)\\\"";
        java.util.regex.Matcher matcher = java.util.regex.Pattern.compile(pattern).matcher(json);
        return matcher.find() ? matcher.group(1) : "";
    }

    // Compares semantic versions (e.g., 1.2.3 > 1.2.2)
    private static boolean isNewerVersion(String latest, String current) {
        String[] l = latest.split("\\.");
        String[] c = current.split("\\.");
        for (int i = 0; i < Math.max(l.length, c.length); i++) {
            int lv = i < l.length ? Integer.parseInt(l[i]) : 0;
            int cv = i < c.length ? Integer.parseInt(c[i]) : 0;
            if (lv > cv) return true;
            if (lv < cv) return false;
        }
        return false;
    }

    private static void addShutdownHook(File oldJar, File newJar) {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            try {
                if (oldJar == null || !oldJar.exists() || oldJar.isDirectory()) {
                    Wynnutils.LOGGER.warn("Updater: Mod jar file not found or incorrect.");
                    return;
                }
                String os = System.getProperty("os.name").toLowerCase();
                File scriptFile;
                String scriptContent;
                if (os.contains("win")) {
                    // Windows batch file
                    scriptFile = new File(newJar.getParentFile(), "wynnutils_update.bat");
                    scriptContent =
                            "@echo off\n" +
                            "setlocal enabledelayedexpansion\n" +
                            "set COUNT=0\n" +
                            "set OLDJAR=\"" + oldJar.getAbsolutePath() + "\"\n" +
                            "set NEWJAR=\"" + newJar.getAbsolutePath() + "\"\n" +
                            ":waitloop\n" +
                            "del %OLDJAR% >nul 2>&1\n" +
                            "if exist %OLDJAR% (\n" +
                            "    set /a COUNT+=1\n" +
                            "    timeout /t 1 >nul\n" +
                            "    goto waitloop\n" +
                            ")\n" +
                            "rename %NEWJAR% " + oldJar.getName() + "\n" +
                            "del \"%~f0\"\n" +
                            "exit\n";
                } else {
                    // Unix shell script
                    scriptFile = new File(newJar.getParentFile(), "wynnutils_update.sh");
                    scriptContent =
                            "#!/bin/sh\n" +
                            "COUNT=0\n" +
                            "OLDJAR=\"" + oldJar.getAbsolutePath() + "\"\n" +
                            "NEWJAR=\"" + newJar.getAbsolutePath() + "\"\n" +
                            "while [ -f \"$OLDJAR\" ]; do\n" +
                            "  rm -f \"$OLDJAR\"\n" +
                            "  COUNT=$((COUNT+1))\n" +
                            "  sleep 1\n" +
                            "done\n" +
                            "mv \"$NEWJAR\" \"$OLDJAR\"\n" +
                            "rm -- \"$0\"\n";
                }

                try (FileWriter fw = new FileWriter(scriptFile)) {
                    fw.write(scriptContent);
                }
                if (!os.contains("win")) {
                    // Make shell script executable
                    scriptFile.setExecutable(true);
                }

                // Launch the script
                if (os.contains("win")) {
                    new ProcessBuilder("cmd", "/c", "start", "", "/b", scriptFile.getAbsolutePath()).start();
                } else {
                    new ProcessBuilder("sh", scriptFile.getAbsolutePath()).start();
                }

                Wynnutils.LOGGER.info("Updater: Launched update script: " + scriptFile.getAbsolutePath());
            } catch (IOException e) {
                Wynnutils.LOGGER.error("Updater: Cannot apply update!", e);
                ErrorReporter.reportError("Updater cannot apply update", e.getMessage() + ExceptionUtils.getStackTrace(e));
            }
        }));
    }
} 