package com.ifallious.utils.wynntils;

import com.ifallious.event.GlobalEventBus;
import com.ifallious.event.RaidEvent;
import com.ifallious.event.TickEvent;
import com.wynntils.core.components.Models;
import meteordevelopment.orbit.EventHandler;

public class RaidUtils {
    private RaidEvent lastRaidEvent;
    public RaidUtils() {
        GlobalEventBus.subscribe(this);
    }

    @EventHandler
    public void onTick(TickEvent e) {
        if (Models.Raid.getCurrentRaid() == null) return;
        RaidEvent raidEvent = new RaidEvent(Models.Raid.isInBuffRoom(), Models.Raid.isInIntermissionRoom(), Models.Raid.getCurrentRaid().getRaidKind(), Models.Raid.getCurrentRoomName());
        if (lastRaidEvent == null) {
            GlobalEventBus.post(raidEvent);
            lastRaidEvent = raidEvent;
        } else if (lastRaidEvent != raidEvent) {
            GlobalEventBus.post(raidEvent);
            lastRaidEvent = raidEvent;
        }
    }
}
