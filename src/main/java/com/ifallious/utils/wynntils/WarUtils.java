package com.ifallious.utils.wynntils;
import com.ifallious.utils.ErrorReporter;
import com.wynntils.core.components.Models;
import org.apache.commons.lang3.exception.ExceptionUtils;

/**
 * Utility methods for interacting with Wynncraft guild war mechanics
 */
public class WarUtils {
    /**
     * Get the remaining time until the next aura in a guild war
     * @return The time in ticks until the next aura
     */
    public static long getAuraTimer() {
        try {
            return Models.GuildWarTower.getRemainingTimeUntilAura();
        } catch (Exception e) {
            ErrorReporter.reportError("WarUtils getAuraTimer failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
            return 0;
        }
    }
    /**
     * Check if the player is currently in an active guild war
     * @return True if a war is active, false otherwise
     */
    public static boolean isInWar() {
        try {
            return Models.War.isWarActive();
        } catch (Exception e) {
            ErrorReporter.reportError("WarUtils isInWar failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
            return false;
        }
    }
}
