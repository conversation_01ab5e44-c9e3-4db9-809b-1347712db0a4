package com.ifallious.utils.wynntils;
import com.ifallious.utils.ErrorReporter;
import com.wynntils.core.components.Models;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.apache.commons.lang3.exception.ExceptionUtils;

public class StatusEffectUtils {
    /**
     * Check if the player has a status effect
     * @param effectName The name of the status effect to check for
     * @return True if the player has the status effect, false otherwise
     */
    public static boolean hasStatusEffect(String effectName) {
        try {
            if (effectName == null) {
                return false;
            }
            return Models.StatusEffect.getStatusEffects().stream().anyMatch(effect -> {
                try {
                    return effect.getName().equals(effectName);
                } catch (Exception e) {
                    ErrorReporter.reportError("StatusEffectUtils effect name check failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
                    return false;
                }
            });
        } catch (Exception e) {
            ErrorReporter.reportError("StatusEffectUtils hasStatusEffect failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
            return false;
        }
    }

    /**
     * Get a status effect by name
     * @param name The name of the status effect to find
     * @return A StatusEffect object containing information about the effect
     */
    public static StatusEffect getStatusEffect(String name) {
        try {
            if (name == null) {
                return null;
            }
            var effects = Models.StatusEffect.getStatusEffects();
            for (var effect : effects) {
                try {
                    String effectNameStr = effect.getName().getStringWithoutFormatting();
                    if (!effectNameStr.equals(name)) continue;
                    String timeStr = effect.getDisplayedTime().toString();
                    Pattern timePattern = Pattern.compile("\\((\\d{2}):(\\d{2})\\)");
                    Matcher timeMatcher = timePattern.matcher(timeStr);

                    if (timeMatcher.find()) {
                        int minutes = Integer.parseInt(timeMatcher.group(1));
                        int seconds = Integer.parseInt(timeMatcher.group(2));
                        return new StatusEffect(effectNameStr, true, minutes, seconds);
                    }
                } catch (Exception e) {
                    ErrorReporter.reportError("StatusEffectUtils effect processing failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
                }
            }
            return null;
        } catch (Exception e) {
            ErrorReporter.reportError("StatusEffectUtils getStatusEffect failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
            return null;
        }
    }
}
