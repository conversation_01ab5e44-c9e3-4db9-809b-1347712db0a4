package com.ifallious.utils.wynntils;

/**
 * Represents a status effect in Wynncraft
 */
public class StatusEffect {
    private String name;
    private boolean included;
    private int minutes;
    private int seconds;

    /**
     * Create a new StatusEffect
     * @param name The name of the effect
     * @param included Whether the effect is active
     * @param minutes Minutes remaining on the effect
     * @param seconds Seconds remaining on the effect
     */
    public StatusEffect(String name, boolean included, int minutes, int seconds) {
        this.name = name;
        this.included = included;
        this.minutes = minutes;
        this.seconds = seconds;
    }

    /**
     * Get the name of the effect
     * @return The effect name
     */
    public String getName() {
        return name;
    }

    /**
     * Check if the effect is active
     * @return True if the effect is active, false otherwise
     */
    public boolean isIncluded() {
        return included;
    }

    /**
     * Get the minutes remaining on the effect
     * @return Minutes remaining
     */
    public int getMinutes() {
        return minutes;
    }

    /**
     * Get the seconds remaining on the effect
     * @return Seconds remaining
     */
    public int getSeconds() {
        return seconds;
    }

    /**
     * Get the total time remaining in seconds
     * @return Total seconds remaining
     */
    public int getTotalSeconds() {
        return minutes * 60 + seconds;
    }

    /**
     * Get a formatted string of the time remaining
     * @return Time remaining in MM:SS format
     */
    public String getFormattedTime() {
        return String.format("%02d:%02d", minutes, seconds);
    }
}
