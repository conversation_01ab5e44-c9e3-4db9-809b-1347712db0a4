package com.ifallious.utils.render;

import com.ifallious.Wynnutils;
import com.ifallious.utils.ErrorReporter;
import gg.essential.universal.UMinecraft;
import net.minecraft.util.Formatting;
import org.apache.commons.lang3.exception.ExceptionUtils;

/**
 * Represents a visual timer for a single trap
 * Handles the countdown display and automatic cleanup
 */
public class RenderTrapTimer {
    private final int trapX, trapY, trapZ;
    private final long startTime;
    private final long duration; // Duration in milliseconds (30 seconds = 30000ms)
    private TextDisplay textDisplay;
    private boolean isActive;
    private long lastUpdateTime;
    
    // Visual configuration
    private static final double TIMER_HEIGHT_OFFSET = 0; // Height above trap
    private static final int UPDATE_INTERVAL_MS = 250; // Update every 250ms for smooth countdown
    private static final int BACKGROUND_COLOR = 0x40000000; // Semi-transparent black
    
    /**
     * Create a new trap timer
     * @param trapX X coordinate of the trap
     * @param trapY Y coordinate of the trap  
     * @param trapZ Z coordinate of the trap
     * @param durationSeconds Duration of the trap in seconds
     */
    public RenderTrapTimer(int trapX, int trapY, int trapZ, int durationSeconds) {
        this.trapX = trapX;
        this.trapY = trapY;
        this.trapZ = trapZ;
        this.startTime = System.currentTimeMillis();
        this.duration = durationSeconds * 1000L; // Convert to milliseconds
        this.isActive = true;
        this.lastUpdateTime = 0;
        
        createTextDisplay();
        updateDisplay();
    }
    
    /**
     * Create the text display entity for this timer
     */
    private void createTextDisplay() {
        try {
            // Position the text display above the trap
            double displayX = trapX + 0.5; // Center on block
            double displayY = trapY + TIMER_HEIGHT_OFFSET;
            double displayZ = trapZ + 0.5; // Center on block
            
            // Create with initial text
            String initialText = formatTimeText(getRemainingSeconds());
            textDisplay = new TextDisplay(displayX, displayY, displayZ, initialText, BACKGROUND_COLOR);
            
        } catch (Exception e) {
            Wynnutils.LOGGER.error("Failed to create trap timer display", e);
            ErrorReporter.reportError("TrapTimer display creation failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
            isActive = false;
        }
    }
    
    /**
     * Update the timer display if enough time has passed
     * @return true if timer is still active, false if expired
     */
    public boolean update() {
        if (!isActive) {
            return false;
        }
        
        try {
            long currentTime = System.currentTimeMillis();
            
            // Check if timer has expired
            if (currentTime >= startTime + duration) {
                cleanup();
                return false;
            }
            
            // Update display if enough time has passed
            if (currentTime - lastUpdateTime >= UPDATE_INTERVAL_MS) {
                updateDisplay();
                lastUpdateTime = currentTime;
            }
            
            return true;
            
        } catch (Exception e) {
            Wynnutils.LOGGER.error("TrapTimer update failed for trap at [{}, {}, {}]", trapX, trapY, trapZ, e);
            ErrorReporter.reportError("TrapTimer update failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
            cleanup();
            return false;
        }
    }
    
    /**
     * Update the text display with current remaining time
     */
    private void updateDisplay() {
        if (textDisplay == null) {
            return;
        }
        
        try {
            int remainingSeconds = getRemainingSeconds();
            String timeText = formatTimeText(remainingSeconds);
            textDisplay.setText(timeText);
            
        } catch (Exception e) {
            Wynnutils.LOGGER.error("Failed to update trap timer display", e);
            ErrorReporter.reportError("TrapTimer display update failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
        }
    }
    
    /**
     * Get the remaining time in seconds
     * @return Remaining seconds (0 if expired)
     */
    private int getRemainingSeconds() {
        long currentTime = System.currentTimeMillis();
        long elapsed = currentTime - startTime;
        long remaining = duration - elapsed;
        return Math.max(0, (int) (remaining / 1000));
    }
    
    /**
     * Format the time text with appropriate styling
     * @param seconds Remaining seconds
     * @return Formatted text string
     */
    private String formatTimeText(int seconds) {
        // Choose color and style based on remaining time
        String formattedText;
        if (seconds <= 5) {
            // Red and bold for last 5 seconds
            formattedText = Formatting.RED + "" + Formatting.BOLD + seconds + "s";
        } else if (seconds <= 10) {
            // Yellow for last 10 seconds
            formattedText = Formatting.YELLOW + "" + Formatting.BOLD + seconds + "s";
        } else {
            // Green for normal time
            formattedText = Formatting.GREEN + "" + seconds + "s";
        }

        return formattedText;
    }
    
    /**
     * Check if this timer is for the specified trap coordinates
     * @param x X coordinate
     * @param y Y coordinate
     * @param z Z coordinate
     * @return true if coordinates match
     */
    public boolean isForTrap(int x, int y, int z) {
        return trapX == x && trapY == y && trapZ == z;
    }
    
    /**
     * Get the distance from the player to this timer
     * @return Distance in blocks, or -1 if player is null
     */
    public double getDistanceFromPlayer() {
        try {
            if (UMinecraft.getPlayer() == null) {
                return -1;
            }
            
            double playerX = UMinecraft.getPlayer().getX();
            double playerY = UMinecraft.getPlayer().getY();
            double playerZ = UMinecraft.getPlayer().getZ();
            
            double dx = trapX + 0.5 - playerX;
            double dy = trapY + TIMER_HEIGHT_OFFSET - playerY;
            double dz = trapZ + 0.5 - playerZ;
            
            return Math.sqrt(dx * dx + dy * dy + dz * dz);
            
        } catch (Exception e) {
            return -1;
        }
    }
    
    /**
     * Check if timer is active
     * @return true if active
     */
    public boolean isActive() {
        return isActive;
    }
    
    /**
     * Get trap coordinates
     * @return Array of [x, y, z]
     */
    public int[] getTrapCoordinates() {
        return new int[]{trapX, trapY, trapZ};
    }
    
    /**
     * Manually cleanup this timer (for early removal)
     */
    public void cleanup() {
        try {
            isActive = false;
            
            if (textDisplay != null) {
                textDisplay.remove();
                textDisplay = null;
            }
            
        } catch (Exception e) {
            Wynnutils.LOGGER.error("TrapTimer cleanup failed for trap at [{}, {}, {}]", trapX, trapY, trapZ, e);
            ErrorReporter.reportError("TrapTimer cleanup failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
        }
    }
}
