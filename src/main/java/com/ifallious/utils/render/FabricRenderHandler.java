package com.ifallious.utils.render;
import net.fabricmc.fabric.api.client.rendering.v1.HudRenderCallback;
import net.fabricmc.fabric.api.client.rendering.v1.WorldRenderEvents;

public class FabricRenderHandler {
    public static void initialize() {
        TextRenderer3D.initialize();
        // Try this stage instead
        WorldRenderEvents.END.register(context -> {
            //RenderUtils.renderText(context.matrixStack());
            TextRenderer3D.renderAll(context.matrixStack());
        });
        HudRenderCallback.EVENT.register((context, tickDelta) -> {

        });
    }
}
