package com.ifallious.utils.render;

import gg.essential.universal.UChat;
import gg.essential.universal.UMinecraft;
import net.minecraft.client.MinecraftClient;
import net.minecraft.entity.Entity;
import net.minecraft.entity.EntityType;
import net.minecraft.entity.SpawnReason;
import net.minecraft.entity.decoration.DisplayEntity;
import net.minecraft.entity.decoration.DisplayEntity.TextDisplayEntity;
import net.minecraft.text.Text;

public class TextDisplay {
    private TextDisplayEntity textDisplay;

    /**
     * Create a new text display at the given coordinates
     * @param x X coordinate
     * @param y Y coordinate
     * @param z Z coordinate
     * @param text The text to display
     */
    public TextDisplay(double x, double y, double z, String text) {
        this(x, y, z, text, 0x00000000);
    }

    /**
     * Create a new text display at the given coordinates with custom background
     * @param x X coordinate
     * @param y Y coordinate
     * @param z Z coordinate
     * @param text The text to display
     * @param backgroundColor Background color for the text display
     */
    public TextDisplay(double x, double y, double z, String text, int backgroundColor) {
        try {
            MinecraftClient mc = UMinecraft.getMinecraft();
            textDisplay = EntityType.TEXT_DISPLAY.create(UMinecraft.getWorld(), SpawnReason.COMMAND);
            textDisplay.setText(Text.of(text));
            textDisplay.refreshPositionAndAngles(x, y, z, 0.0F, 0.0F);
            textDisplay.setTextOpacity((byte) 1.0f);
            textDisplay.setBackground(backgroundColor);
            textDisplay.setBillboardMode(DisplayEntity.BillboardMode.VERTICAL);
            mc.world.addEntity(textDisplay);
        } catch (Exception e) {
            UChat.chat("Failed to spawn text display: " + e.getMessage());
        }
    }

    /**
     * Get the underlying TextDisplayEntity
     * @return The TextDisplayEntity
     */
    public TextDisplayEntity getTextDisplay() {
        return textDisplay;
    }

    /**
     * Remove the text display
     */
    public void remove() {
        textDisplay.remove(Entity.RemovalReason.DISCARDED);
    }

    /**
     * Set the text to display
     * @param text The text to display
     */
    public void setText(String text) {
        textDisplay.setText(Text.of(text));
    }

    /**
     * Set the position of the text display
     * @param x X coordinate
     * @param y Y coordinate
     * @param z Z coordinate
     */
    public void setPosition(double x, double y, double z) {
        textDisplay.updatePosition(x, y, z);
    }
}
