package com.ifallious.utils.render;

import com.ifallious.event.GlobalEventBus;
import com.ifallious.event.Render3DEvent;
import meteordevelopment.meteorclient.renderer.ShapeMode;
import meteordevelopment.meteorclient.utils.render.color.Color;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.util.math.BlockPos;

import java.util.ArrayList;
import java.util.List;

public class BlockRenderer {
    // Store the blocks you want to render
    private final List<BlockPos> blocksToRender = new ArrayList<>();
    
    public BlockRenderer() {
        GlobalEventBus.subscribe(this);
    }
    
    // Method to add a block at specific position
    public void addBlock(int x, int y, int z) {
        blocksToRender.add(new BlockPos(x, y, z));
    }
    
    // Method to remove a block
    public void removeBlock(int x, int y, int z) {
        blocksToRender.remove(new BlockPos(x, y, z));
    }
    
    // Method to clear all blocks
    public void clearBlocks() {
        blocksToRender.clear();
    }
    
    @EventHandler
    private void onRender(Render3DEvent event) {
        // This is where the magic happens - draw each block
        for (BlockPos pos : blocksToRender) {
            // Draw a box at the block position
            event.renderer.box(
                pos.getX(), pos.getY(), pos.getZ(),           // Start coordinates
                pos.getX() + 1, pos.getY() + 1, pos.getZ() + 1, // End coordinates
                new Color(255, 0, 0, 50),                     // Side color (red with transparency)
                new Color(255, 0, 0, 255),                    // Line color (solid red)
                ShapeMode.Both,                               // Draw both lines and sides
                0                                             // No excluded directions
            );
        }
    }
}