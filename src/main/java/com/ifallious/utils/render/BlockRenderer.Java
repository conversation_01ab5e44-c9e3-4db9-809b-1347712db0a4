package com.ifallious.utils.render;

import com.ifallious.event.GlobalEventBus;
import com.ifallious.event.Render3DEvent;
import meteordevelopment.meteorclient.renderer.ShapeMode;
import meteordevelopment.meteorclient.utils.render.color.Color;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.util.math.BlockPos;

import java.util.HashMap;
import java.util.Map;

public class BlockRenderer {
    // Inner class to store rendering parameters for each block
    public static class BlockRenderData {
        private final Color sideColor;
        private final Color lineColor;
        private final ShapeMode shapeMode;

        public BlockRenderData(Color sideColor, Color lineColor, ShapeMode shapeMode) {
            this.sideColor = sideColor;
            this.lineColor = lineColor;
            this.shapeMode = shapeMode;
        }

        public Color getSideColor() { return sideColor; }
        public Color getLineColor() { return lineColor; }
        public ShapeMode getShapeMode() { return shapeMode; }
    }

    // Store blocks with their individual rendering parameters
    private final Map<BlockPos, BlockRenderData> blocksToRender = new HashMap<>();

    // Default rendering parameters (used when no specific parameters are provided)
    private Color defaultSideColor = new Color(255, 0, 0, 50);    // Default: red with transparency
    private Color defaultLineColor = new Color(255, 0, 0, 255);   // Default: solid red
    private ShapeMode defaultShapeMode = ShapeMode.Both;          // Default: draw both lines and sides
    
    public BlockRenderer() {
        GlobalEventBus.subscribe(this);
    }

    // Constructor with custom default colors and shape mode
    public BlockRenderer(Color defaultSideColor, Color defaultLineColor, ShapeMode defaultShapeMode) {
        this.defaultSideColor = defaultSideColor;
        this.defaultLineColor = defaultLineColor;
        this.defaultShapeMode = defaultShapeMode;
        GlobalEventBus.subscribe(this);
    }
    
    // Method to add a block at specific position
    public void addBlock(int x, int y, int z) {
        blocksToRender.add(new BlockPos(x, y, z));
    }
    
    // Method to remove a block
    public void removeBlock(int x, int y, int z) {
        blocksToRender.remove(new BlockPos(x, y, z));
    }
    
    // Method to clear all blocks
    public void clearBlocks() {
        blocksToRender.clear();
    }
    
    @EventHandler
    private void onRender(Render3DEvent event) {
        // This is where the magic happens - draw each block
        for (BlockPos pos : blocksToRender) {
            // Draw a box at the block position
            event.renderer.box(
                pos.getX(), pos.getY(), pos.getZ(),           // Start coordinates
                pos.getX() + 1, pos.getY() + 1, pos.getZ() + 1, // End coordinates
                new Color(255, 0, 0, 50),                     // Side color (red with transparency)
                new Color(255, 0, 0, 255),                    // Line color (solid red)
                ShapeMode.Both,                               // Draw both lines and sides
                0                                             // No excluded directions
            );
        }
    }
}