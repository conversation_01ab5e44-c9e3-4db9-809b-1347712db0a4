package com.ifallious.utils.render;

import com.ifallious.event.GlobalEventBus;
import com.ifallious.event.Render3DEvent;
import meteordevelopment.meteorclient.renderer.ShapeMode;
import meteordevelopment.meteorclient.utils.render.color.Color;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.util.math.BlockPos;

import java.util.ArrayList;
import java.util.List;

public class BlockRenderer {
    // Store the blocks you want to render
    private final List<BlockPos> blocksToRender = new ArrayList<>();

    // Configurable rendering parameters
    private Color sideColor = new Color(255, 0, 0, 50);    // Default: red with transparency
    private Color lineColor = new Color(255, 0, 0, 255);   // Default: solid red
    private ShapeMode shapeMode = ShapeMode.Both;          // Default: draw both lines and sides

    public BlockRenderer() {
        GlobalEventBus.subscribe(this);
    }

    // Constructor with custom colors and shape mode
    public BlockRenderer(Color sideColor, Color lineColor, ShapeMode shapeMode) {
        this.sideColor = sideColor;
        this.lineColor = lineColor;
        this.shapeMode = shapeMode;
        GlobalEventBus.subscribe(this);
    }
    
    // Method to add a block at specific position
    public void addBlock(int x, int y, int z) {
        blocksToRender.add(new BlockPos(x, y, z));
    }
    
    // Method to remove a block
    public void removeBlock(int x, int y, int z) {
        blocksToRender.remove(new BlockPos(x, y, z));
    }
    
    // Method to clear all blocks
    public void clearBlocks() {
        blocksToRender.clear();
    }

    // Getter and setter methods for rendering parameters
    public Color getSideColor() {
        return sideColor;
    }

    public void setSideColor(Color sideColor) {
        this.sideColor = sideColor;
    }

    public Color getLineColor() {
        return lineColor;
    }

    public void setLineColor(Color lineColor) {
        this.lineColor = lineColor;
    }

    public ShapeMode getShapeMode() {
        return shapeMode;
    }

    public void setShapeMode(ShapeMode shapeMode) {
        this.shapeMode = shapeMode;
    }

    // Convenience method to set both colors at once
    public void setColors(Color sideColor, Color lineColor) {
        this.sideColor = sideColor;
        this.lineColor = lineColor;
    }
    
    @EventHandler
    private void onRender(Render3DEvent event) {
        // This is where the magic happens - draw each block
        for (BlockPos pos : blocksToRender) {
            // Draw a box at the block position
            event.renderer.box(
                pos.getX(), pos.getY(), pos.getZ(),           // Start coordinates
                pos.getX() + 1, pos.getY() + 1, pos.getZ() + 1, // End coordinates
                new Color(255, 0, 0, 50),                     // Side color (red with transparency)
                new Color(255, 0, 0, 255),                    // Line color (solid red)
                ShapeMode.Both,                               // Draw both lines and sides
                0                                             // No excluded directions
            );
        }
    }
}