package com.ifallious.utils.config;

public class Config {
    private ConfigFeatures features;
    private ConfigSettings settings;
    private String selectedCycle;
    private String version;

    public Config() {
        this.features = new ConfigFeatures();
        this.settings = new ConfigSettings();
        this.selectedCycle = "";
        this.version = "1.0.0";
    }

    /**
     * Get the features configuration
     * @return The features configuration
     */
    public ConfigFeatures getFeatures() {
        return features;
    }

    /**
     * Set the features configuration
     * @param features The features configuration to set
     */
    public void setFeatures(ConfigFeatures features) {
        this.features = features;
    }

    /**
     * Get the selected spell cycle
     * @return The selected cycle name
     */
    public String getSelectedCycle() {
        return selectedCycle;
    }

    /**
     * Set the selected spell cycle
     * @param selectedCycle The cycle name to set
     */
    public void setSelectedCycle(String selectedCycle) {
        this.selectedCycle = selectedCycle;
    }

    /**
     * Get the configuration version
     * @return The version string
     */
    public String getVersion() {
        return version;
    }

    /**
     * Set the configuration version
     * @param version The version string to set
     */
    public void setVersion(String version) {
        this.version = version;
    }

    /**
     * Get the settings configuration
     * @return The settings configuration
     */
    public ConfigSettings getSettings() {
        return settings;
    }

    /**
     * Set the settings configuration
     * @param settings The settings configuration to set
     */
    public void setSettings(ConfigSettings settings) {
        this.settings = settings;
    }
}
