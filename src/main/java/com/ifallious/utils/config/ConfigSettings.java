package com.ifallious.utils.config;

/**
 * Configuration class for numerical settings in the mod
 *
 * All fields are public for direct access, eliminating the need for getters and setters.
 * The ConfigManager uses reflection to access these fields by name.
 */
public class ConfigSettings {
    // Velocity multiplier for the StrongerCharge feature
    public double chargeVelocityMultiplier = 1.2;
    public double autoTransferThreshold = 150;
    public String AIModel = null;
    public String AIEndpoint = null;
    public String AISystemPrompt = null;
    public double AITemperature = 0.7;
    public String recruitingLevel = "100";
    public String recruitingMessage = null;
    public ConfigSettings() {
    }
}
