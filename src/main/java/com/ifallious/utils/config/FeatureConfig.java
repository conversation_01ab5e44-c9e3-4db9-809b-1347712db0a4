package com.ifallious.utils.config;

public class FeatureConfig {
    /**
     * Check if a feature is enabled
     * @param featureName The name of the feature
     * @return True if the feature is enabled, false otherwise
     */
    public static boolean isEnabled(String featureName) {
        Boolean value = ConfigManager.getFeature(featureName);
        return value != null && value;
    }
    
    /**
     * Toggle a feature
     * @param featureName The name of the feature
     * @return The new state of the feature (true if enabled, false if disabled)
     */
    public static boolean toggle(String featureName) {
        boolean currentState = isEnabled(featureName);
        ConfigManager.setFeature(featureName, !currentState);
        return !currentState;
    }
}
