package com.ifallious.utils.minecraft;

import net.minecraft.client.network.ClientPlayNetworkHandler;
import net.minecraft.network.packet.Packet;

import static com.ifallious.Wynnutils.mc;

public class PacketUtils {
    /**
     * Send a packet to the server
     * @param packet The packet to send
     */
    public static void sendPacket(Packet<?> packet) {
        ClientPlayNetworkHandler networkHandler = mc.getNetworkHandler();
        if (networkHandler == null) return;
        networkHandler.sendPacket(packet);
    }
}
