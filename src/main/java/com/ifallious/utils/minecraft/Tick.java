package com.ifallious.utils.minecraft;

import com.ifallious.event.GlobalEventBus;
import com.ifallious.event.TickEvent;
import com.ifallious.utils.ErrorReporter;
import net.fabricmc.fabric.api.client.event.lifecycle.v1.ClientTickEvents;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.apache.commons.lang3.exception.ExceptionUtils;

/**
 * Utility class for tracking and managing game ticks
 */
public class Tick {
    private static long Tick = 0;
    private static final Map<Long, List<Runnable>> scheduledTasks = new ConcurrentHashMap<>();

    /**
     * Initialize the tick system and register the tick event handler
     */
    public Tick() {
            ClientTickEvents.END_CLIENT_TICK.register(client -> {
                    Tick++;
                    GlobalEventBus.post(new TickEvent(Tick));

                    List<Runnable> tasks = scheduledTasks.remove(Tick);
                    if (tasks != null) {
                        for (Runnable task : tasks) {
                            try {
                                task.run();
                            } catch (Exception e) {
                                ErrorReporter.reportError("Tick scheduled task execution failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
                            }
                        }
                    }
            });
    }

    /**
     * Get the current tick count
     * @return The current tick count
     */
    public static long now() {
        return Tick;
    }

    /**
     * Calculate how many ticks have passed since a starting tick
     * @param start The starting tick
     * @return The number of ticks that have passed
     */
    public static long since(long start) {
        return Tick - start;
    }

    /**
     * Calculate the number of ticks between two tick values
     * @param start The starting tick
     * @param end The ending tick
     * @return The number of ticks between start and end
     */
    public static long since(long start, long end) {
        return end - start;
    }

    /**
     * Schedule a task to run after a specified delay in ticks
     * @param delay Number of ticks to wait before executing the task
     * @param task The task to execute
     * @return The tick when the task will be executed
     */
    public static long schedule(long delay, Runnable task) {
        try {
            if (task == null) {
                ErrorReporter.reportError("Tick schedule failed", "Task is null");
                return 0;
            }
            long executionTick = Tick + delay;
            scheduledTasks.computeIfAbsent(executionTick, k -> new ArrayList<>()).add(task);
            return executionTick;
        } catch (Exception e) {
            ErrorReporter.reportError("Tick schedule failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
            return 0;
        }
    }
}
