package com.ifallious.utils.minecraft;

import com.ifallious.event.GlobalEventBus;
import com.ifallious.event.GuildChatMessageEvent;
import com.ifallious.event.PacketEvent;
import com.wynntils.core.components.Handler;
import com.wynntils.core.text.StyledText;
import com.wynntils.mc.event.ChatPacketReceivedEvent;
import gg.essential.universal.UChat;
import meteordevelopment.orbit.EventHandler;
import com.ifallious.event.TickEvent;
import net.minecraft.network.packet.s2c.play.GameMessageS2CPacket;

import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Parses guild chat messages (including multi-line) and emits a single aggregated event.
 * Refactored to use a FIFO queue and process messages sequentially per game tick.
 */
public final class ChatHandler extends Handler {
    // Regex to match the start of a guild message.
    // Examples come through with formatting codes as '§', but we normalize to '&' before matching.
    // Be tolerant: match username introduced by &3 anywhere in the line, then optional formatting codes, then message.
    private static final Pattern GUILD_START_PATTERN = Pattern.compile(
            "(?:&[0-9a-fk-orA-FK-OR]|\\p{So}|\\p{Cn})*" + // skip leading formatting
                    "\\s*&[0-9a-fk-orA-FK-OR]*((?:&[0-9a-fk-orA-FK-OR]|[A-Za-z0-9_])+):" +
                    "&[0-9a-fk-orA-FK-OR]*\\s*(.*)"
    );

    // Simple color code stripper for both § and & codes
    private static final Pattern COLOR_CODE_PATTERN = Pattern.compile("(?i)[§&][0-9A-FK-OR]");

    // Deduplicate identical raw lines that can arrive multiple times within a short window
    private final Map<String, Long> recentLines = new LinkedHashMap<>(32, 0.75f, true);

    // Queue to buffer incoming raw chat lines (normalized with & codes) in FIFO order
    private final Queue<String> messageQueue = new ConcurrentLinkedQueue<>();

    // Aggregation state for a currently-building guild message
    private static final long AGG_WINDOW_MS = 50; // window for combining split lines
    private static final long CONTINUATION_GRACE_MS = 80; // only accept continuations very shortly after previous part
    private Aggregation currentAggregation;

    private static class Aggregation {
        final String username;
        final StringBuilder content = new StringBuilder();
        long lastUpdateMs;

        Aggregation(String username, String firstPart, long now) {
            this.username = username;
            if (firstPart != null && !firstPart.isEmpty()) this.content.append(firstPart);
            this.lastUpdateMs = now;
        }

        void append(String part, long now) {
            if (part == null || part.isEmpty()) return;
            if (content.length() > 0) content.append(' ');
            content.append(part);
            lastUpdateMs = now;
        }

        String message() { return content.toString().trim(); }
    }

    public ChatHandler() {
        GlobalEventBus.subscribe(this);
    }

    // Enqueue incoming system chat packets; actual processing happens on tick thread
    @EventHandler
    public void onPacketReceived(PacketEvent.PacketReceiveEvent event) {
        if (event.packet instanceof GameMessageS2CPacket packet) {
            if (packet.overlay()) return;
            String rawWithCodes = StyledText.fromComponent(packet.content()).getString().replace('§', '&');
            messageQueue.add(rawWithCodes);
        }
    }

    // Process queued messages sequentially each game tick to avoid simultaneous processing
    @EventHandler
    public void onTick(TickEvent e) {
        flushIfExpired(System.currentTimeMillis());
        long now;
        if (messageQueue.isEmpty()) return;
        // Process all pending messages this tick in FIFO order
        for (String line = messageQueue.poll(); line != null; line = messageQueue.poll()) {
            now = System.currentTimeMillis();
            handleRawLine(line, now);
        }
    }

    private void handleRawLine(String rawWithCodes, long now) {
        // Drop duplicates arriving within a short period
        if (isDuplicate(rawWithCodes, now)) return;

        // Before handling this line, flush stale aggregation if its window expired
        flushIfExpired(now);

        // Try to match a guild start line
        Matcher m = GUILD_START_PATTERN.matcher(rawWithCodes);
        if (m.find()) {
            String[] parts = rawWithCodes.split(":");
            String username = parts[0].substring(parts[0].lastIndexOf("&3") + 2);
            String message = Arrays.stream(parts)
                    .skip(1)
                    .collect(Collectors.joining(""));
            // If we were already aggregating and it's the same user within window, continue; otherwise flush and start new
            flushCurrent();
            currentAggregation = new Aggregation(username, message.replaceAll("[^\\x00-\\x7F]", ""), now);
            return;
        }

        // Otherwise, not guild related; if we have a pending aggregation and the window has passed, flush it
        flushIfExpired(now);
    }

    private boolean isDuplicate(String line, long now) {
        // Purge old entries
        recentLines.values().removeIf(ts -> now - ts > 250);
        Long last = recentLines.get(line);
        recentLines.put(line, now);
        return last != null && (now - last) < 30; // treat as duplicate if identical within 30ms
    }

    private void flushIfExpired(long now) {
        if (currentAggregation != null && now - currentAggregation.lastUpdateMs > AGG_WINDOW_MS) {
            flushCurrent();
        }
    }

    private void flushCurrent() {
        if (currentAggregation == null) return;
        String msg = currentAggregation.message();
        if (!msg.isBlank()) {
            GlobalEventBus.post(new GuildChatMessageEvent(currentAggregation.username, msg));
        }
        currentAggregation = null;
    }

    private boolean isGuildContinuationLine(String rawWithCodes) {
        // Continuations typically retain &b and no &3<user>:
        if (!rawWithCodes.contains("&b")) return false;
        // Heuristic: continuation lines should NOT look like a new username segment
        return !rawWithCodes.matches(".*&3[A-Za-z0-9_]{1,16}:.*$");
    }

    private static String stripColorCodes(String s) {
        if (s == null || s.isEmpty()) return s;
        return COLOR_CODE_PATTERN.matcher(s).replaceAll("").trim();
    }
    private static String cleanContinuationTail(String rawTail) {
        if (rawTail == null || rawTail.isEmpty()) return rawTail;
        // Remove color codes first
        String s = stripColorCodes(rawTail);
        // Drop any leading non-ASCII printable glyphs, then trim leading spaces
        s = s.replaceFirst("^[^\\x20-\\x7E]+", "").stripLeading();
        return s;
    }
}
