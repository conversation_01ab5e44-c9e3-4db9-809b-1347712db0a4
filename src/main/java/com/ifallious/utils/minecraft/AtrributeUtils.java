package com.ifallious.utils.minecraft;
import com.ifallious.Wynnutils;
import com.ifallious.utils.ErrorReporter;
import net.minecraft.entity.attribute.EntityAttribute;
import com.google.common.collect.HashMultimap;
import net.minecraft.entity.attribute.EntityAttributeModifier;
import net.minecraft.registry.entry.RegistryEntry;
import net.minecraft.util.Identifier;
import net.minecraft.entity.attribute.EntityAttributeModifier.Operation;
import org.apache.commons.lang3.exception.ExceptionUtils;

/**
 * Utility class for managing entity attributes
 */
public class AtrributeUtils {
    /**
     * Add a temporary attribute modifier to the player
     * @param id The identifier for the attribute modifier
     * @param attribute The attribute to modify
     * @param value The value to add to the attribute
     */
    public static void addAttribute(String id, RegistryEntry<EntityAttribute> attribute, float value) {
        try {
            if (Wynnutils.mc.player == null) {
                return;
            }
            if (id == null || attribute == null) {
                return;
            }
            Wynnutils.mc.player.getAttributes().addTemporaryModifiers(constructModifiers(Identifier.ofVanilla(id), attribute, value, Operation.ADD_VALUE));
        } catch (Exception e) {
            ErrorReporter.reportError("AtrributeUtils addAttribute failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
        }
    }
    /**
     * Reset a temporary attribute modifier on the player
     * @param id The identifier for the attribute modifier
     * @param attribute The attribute to reset
     */
    public static void resetAttribute(String id, RegistryEntry<EntityAttribute> attribute) {
        try {
            if (Wynnutils.mc.player == null) {
                return;
            }
            if (id == null || attribute == null) {
                return;
            }
            Wynnutils.mc.player.getAttributes().addTemporaryModifiers(constructModifiers(Identifier.ofVanilla(id), attribute, 0.0f, Operation.ADD_VALUE));
        } catch (Exception e) {
            ErrorReporter.reportError("AtrributeUtils resetAttribute failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
        }
    }
    /**
     * Construct attribute modifiers for use with the attribute system
     * @param id The identifier for the attribute modifier
     * @param attribute The attribute to modify
     * @param value The value to apply to the attribute
     * @param operation The operation to use when applying the modifier
     * @return A multimap containing the attribute modifiers
     */
    public static HashMultimap<RegistryEntry<EntityAttribute>, EntityAttributeModifier> constructModifiers(Identifier id, RegistryEntry<EntityAttribute> attribute, float value, Operation operation) {
        try {
            if (id == null || attribute == null || operation == null) {
                ErrorReporter.reportError("AtrributeUtils constructModifiers failed", "ID, attribute, or operation is null");
                return HashMultimap.create();
            }
            HashMultimap<RegistryEntry<EntityAttribute>, EntityAttributeModifier> modifiers = HashMultimap.create();
            EntityAttributeModifier modifier = new EntityAttributeModifier(id, value, operation);
            modifiers.put(attribute, modifier);
            return modifiers;
        } catch (Exception e) {
            ErrorReporter.reportError("AtrributeUtils constructModifiers failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
            return HashMultimap.create();
        }
    }
}
