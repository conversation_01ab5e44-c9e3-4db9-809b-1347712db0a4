package com.ifallious.utils.minecraft;

import com.ifallious.event.GlobalEventBus;
import com.ifallious.event.PacketEvent;
import com.ifallious.event.ParticleEvent;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.network.packet.s2c.play.ParticleS2CPacket;

public class ParticleUtils {
    public ParticleUtils() {
        GlobalEventBus.subscribe(this);
    }

    @EventHandler
    private void onPacketReceive(PacketEvent.PacketReceiveEvent event) {
        if (event.packet instanceof ParticleS2CPacket) {
            var p = (ParticleS2CPacket) event.packet;
            GlobalEventBus.post(new ParticleEvent(p.getX(), p.getY(), p.getZ(), p.getOffsetX(), p.getOffsetY(), p.getOffsetZ(), p.getSpeed(), p.getCount(), p.shouldForceSpawn(), p.isImportant(), p.getParameters()));
        }
    }
}
