package com.ifallious.utils.ai;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.ifallious.utils.ErrorReporter;
import com.ifallious.utils.ai.core.Conversation;
import com.ifallious.utils.ai.model.AIMessage;
import com.ifallious.utils.ai.tools.ToolRegistry;
import com.ifallious.utils.ai.json.ToolCallParser;
import com.ifallious.utils.config.ConfigManager;
import com.ifallious.Wynnutils;
import gg.essential.universal.UChat;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * Enhanced ConversationManager with full tool calling support.
 * This version properly handles tool calls, executes them, and continues the conversation.
 */
public class ToolCallingConversationManager {
    private static final Gson GSON = new Gson();
    private String provider = "local";
    private String model = "openai/gpt-oss-20b";
    private final Executor executor = Executors.newFixedThreadPool(2, r -> {
        Thread t = new Thread(r, "ToolCallingConversationManager-Worker");
        t.setDaemon(true);
        return t;
    });

    private String endpoint = "http://127.0.0.1:1234/v1/chat/completions";
    private double temperature = 0.7;
    private int maxTokens = -1;
    private boolean enableTools = true; // Tools enabled by default

    public final Conversation conversation = new Conversation();

    // Fluent configuration
    public ToolCallingConversationManager withProvider(String provider) { 
        if (provider != null && !provider.isBlank()) this.provider = provider; 
        return this; 
    }
    public ToolCallingConversationManager withModel(String model) { 
        if (model != null && !model.isBlank()) this.model = model; 
        return this; 
    }
    public ToolCallingConversationManager withEndpoint(String endpoint) { 
        if (endpoint != null && !endpoint.isBlank()) this.endpoint = endpoint; 
        return this; 
    }
    public ToolCallingConversationManager withTemperature(double temperature) { 
        this.temperature = Math.max(0.0, Math.min(1.0, temperature)); 
        return this; 
    }
    public ToolCallingConversationManager withMaxTokens(int maxTokens) { 
        this.maxTokens = maxTokens; 
        return this; 
    }
    public ToolCallingConversationManager withSystemPrompt(String prompt) {
        system(prompt); 
        return this;
    }
    public ToolCallingConversationManager withToolsEnabled(boolean enabled) { 
        this.enableTools = enabled; 
        return this; 
    }

    public static ToolCallingConversationManager create(int maxTokens) {
        if (ConfigManager.getSetting("AIModel") == null || ConfigManager.getSetting("AIEndpoint") == null) {
            UChat.chat("Please set up AI Settings first");
            return null;
        }
        return new ToolCallingConversationManager()
                .withProvider("local")
                .withModel((String) ConfigManager.getSetting("AIModel"))
                .withEndpoint((String) ConfigManager.getSetting("AIEndpoint"))
                .withTemperature((Double) ConfigManager.getSetting("AITemperature"))
                .withMaxTokens(maxTokens)
                .withSystemPrompt((String) ConfigManager.getSetting("AISystemPrompt"));
    }

    // Timeouts
    private int connectTimeoutSec = 10;
    private int readTimeoutSec = 60;

    public ToolCallingConversationManager withConnectTimeoutSec(int sec) { 
        this.connectTimeoutSec = Math.max(1, sec); 
        return this; 
    }
    public ToolCallingConversationManager withReadTimeoutSec(int sec) { 
        this.readTimeoutSec = Math.max(1, sec); 
        return this; 
    }

    // Conversation helpers
    public ToolCallingConversationManager system(String content) { 
        conversation.system(content); 
        return this; 
    }
    public void addUserMessage(String content) {
        conversation.user(content);
    }
    public List<AIMessage> getHistory() { 
        return Collections.unmodifiableList(conversation.getMessages()); 
    }
    public void clear() { 
        conversation.clear(); 
    }

    /**
     * Send a user message and get a response, handling tool calls automatically.
     * This method will:
     * 1. Send the user message to the AI
     * 2. If the AI responds with tool calls, execute them
     * 3. Send the tool results back to the AI
     * 4. Return the final AI response
     */
    public CompletableFuture<String> sendMessage(String userMessage) {
        try {
            if (userMessage != null) conversation.user(userMessage);
            return sendWithToolHandling();
        } catch (RuntimeException e) {
            try { 
                ErrorReporter.reportError("ToolCallingConversationManager sendMessage failed", e.getMessage()); 
            } catch (Exception ignored) {}
            throw e;
        }
    }

    public CompletableFuture<String> sendWithToolHandling() {
        return sendViaLocalWithParsing().thenCompose(parsedResponse -> {
            if (parsedResponse.hasToolCalls) {
                // Add the assistant message with tool calls to conversation
                conversation.add(parsedResponse.toAIMessage());

                // Execute all tool calls
                return executeToolCalls(parsedResponse.toolCalls).thenCompose(toolResults -> {
                    // Add tool results to conversation
                    for (int i = 0; i < parsedResponse.toolCalls.size(); i++) {
                        AIMessage.ToolCall toolCall = parsedResponse.toolCalls.get(i);
                        String result = i < toolResults.size() ? toolResults.get(i) : "Tool execution failed";
                        conversation.add(AIMessage.tool(result, toolCall.id));
                    }

                    // Get final response from AI after tool execution
                    return sendViaLocalWithParsing().thenApply(finalResponse -> {
                        conversation.add(finalResponse.toAIMessage());
                        return finalResponse.content != null ? finalResponse.content : "";
                    });
                });
            } else {
                // No tool calls, add response to conversation and return
                conversation.add(parsedResponse.toAIMessage());
                return CompletableFuture.completedFuture(parsedResponse.content != null ? parsedResponse.content : "");
            }
        });
    }

    private CompletableFuture<List<String>> executeToolCalls(List<AIMessage.ToolCall> toolCalls) {
        List<CompletableFuture<String>> toolFutures = new ArrayList<>();

        for (AIMessage.ToolCall toolCall : toolCalls) {
            if ("function".equals(toolCall.type) && toolCall.function != null) {
                Wynnutils.LOGGER.info("Executing tool: {} with args: {}",
                    toolCall.function.name, toolCall.function.arguments);

                CompletableFuture<String> toolFuture = ToolRegistry.executeTool(
                    toolCall.function.name,
                    toolCall.function.arguments
                );
                toolFutures.add(toolFuture);
            } else {
                // Unknown tool call type
                toolFutures.add(CompletableFuture.completedFuture(
                    "{\"status\":\"error\",\"message\":\"Unknown tool call type: " + toolCall.type + "\"}"
                ));
            }
        }

        // Wait for all tool executions to complete
        return CompletableFuture.allOf(toolFutures.toArray(new CompletableFuture[0]))
                .thenApply(v -> toolFutures.stream()
                        .map(CompletableFuture::join)
                        .collect(java.util.stream.Collectors.toList()));
    }

    private boolean isLocal() { 
        return provider == null || provider.equalsIgnoreCase("local"); 
    }

    private CompletableFuture<ToolCallParser.ParsedResponse> sendViaLocalWithParsing() {
        Local client = new Local()
                .withModel(model)
                .withTemperature(temperature)
                .withMaxTokens(maxTokens)
                .withEndpoint(endpoint)
                .withConnectTimeoutSec(connectTimeoutSec)
                .withReadTimeoutSec(readTimeoutSec)
                .withToolsEnabled(enableTools);

        // Convert Conversation -> Local history
        List<Local.Message> localHistory = new ArrayList<>();
        for (AIMessage m : conversation.getMessages()) {
            // Handle different message types properly
            if ("tool".equals(m.role)) {
                // Tool messages need special handling
                Local.Message toolMsg = new Local.Message(m.role, m.content);
                if (m.tool_call_id != null) {
                    toolMsg.tool_call_id = m.tool_call_id;
                }
                localHistory.add(toolMsg);
            } else {
                localHistory.add(new Local.Message(m.role, m.content));
            }
        }
        client.withHistory(localHistory);

        int before = localHistory.size();
        return client.completeWithToolCalls().thenApply(parsedResponse -> {
            int after = conversation.getMessages().size();
            Wynnutils.LOGGER.debug("Conversation messages before: {}, after parsing: {}", before, after);
            return parsedResponse;
        });
    }
}
