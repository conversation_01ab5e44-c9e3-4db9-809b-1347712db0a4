package com.ifallious.utils.ai.core;

import com.google.gson.Gson;
import com.ifallious.utils.ErrorReporter;

import java.nio.charset.StandardCharsets;

/**
 * Base utilities shared by AI clients: Gson instance and error helpers.
 */
public abstract class AIClientBase {
    protected static final Gson GSON = new Gson();

    protected static String safeBody(String body) {
        if (body == null) return "";
        String b = body.trim();
        if (b.length() > 400) b = b.substring(0, 400) + "...";
        return b;
    }

    protected static void reportError(String context, Exception e) {
        try { ErrorReporter.reportError(context, e.getMessage()); } catch (Exception ignored) {}
    }

    public static class AIClientException extends RuntimeException {
        public AIClientException(String message) { super(message); }
        public AIClientException(String message, Throwable cause) { super(message, cause); }
    }

    protected static AIClientException toAIClientException(Exception e) {
        if (e instanceof AIClientException) return (AI<PERSON>lientException) e;
        return new AIClientException(e.getMessage(), e);
    }
}

