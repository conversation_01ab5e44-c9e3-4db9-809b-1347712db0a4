package com.ifallious.utils.ai.core;

import com.ifallious.utils.ai.model.AIMessage;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Simple conversation manager keeping ordered messages.
 */
public class Conversation {
    private final List<AIMessage> history = new ArrayList<>();

    public Conversation add(String role, String content) {
        if (content != null) history.add(new AIMessage(role, content));
        return this;
    }
    public Conversation add(AIMessage msg) {
        if (msg != null) history.add(msg);
        return this;
    }
    public Conversation system(String content) { return add("system", content); }
    public Conversation user(String content) { return add("user", content); }
    public Conversation assistant(String content) { return add("assistant", content); }

    public List<AIMessage> getMessages() { return Collections.unmodifiableList(history); }
    public void clear() { history.clear(); }
}

