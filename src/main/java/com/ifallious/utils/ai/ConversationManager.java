package com.ifallious.utils.ai;

import com.ifallious.utils.ErrorReporter;
import com.ifallious.utils.ai.core.Conversation;
import com.ifallious.utils.ai.model.AIMessage;
import com.ifallious.utils.ai.tools.ToolRegistry;
import com.ifallious.utils.config.ConfigManager;
import gg.essential.universal.UChat;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

import java.util.Map;

/**
 * Unified conversation manager that abstracts provider selection and exposes a simple API.
 * Currently routes to Local for provider="local". External providers are placeholders for future.
 */
public class ConversationManager {
    private String provider = "local"; // local | openai | anthropic (future)
    private String model = "openai/gpt-oss-20b";
    private final Executor executor = Executors.newFixedThreadPool(2, r -> {
        Thread t = new Thread(r, "ConversationManager-Worker");
        t.setDaemon(true);
        return t;
    });

    private String endpoint = "http://127.0.0.1:1234/v1/chat/completions"; // used for local
    private double temperature = 0.7;
    private int maxTokens = -1;
    private boolean enableTools = false; // Enable tool calling

    private final Conversation conversation = new Conversation();

    // Fluent configuration
    public ConversationManager withProvider(String provider) { if (provider != null && !provider.isBlank()) this.provider = provider; return this; }
    public ConversationManager withModel(String model) { if (model != null && !model.isBlank()) this.model = model; return this; }
    public ConversationManager withEndpoint(String endpoint) { if (endpoint != null && !endpoint.isBlank()) this.endpoint = endpoint; return this; }
    public ConversationManager withTemperature(double temperature) { this.temperature = Math.max(0.0, Math.min(1.0, temperature)); return this; }
    public ConversationManager withMaxTokens(int maxTokens) { this.maxTokens = maxTokens; return this; }
    public ConversationManager withSystemPrompt(String prompt) {system(prompt); return this;}
    public ConversationManager withToolsEnabled(boolean enabled) { this.enableTools = enabled; return this; }
    public static ConversationManager create(int maxTokens) {
        if (ConfigManager.getSetting("AIModel") == null || ConfigManager.getSetting("AIEndpoint") == null) {
            UChat.chat("Please set up AI Settings first");
            return null;
        }
        return new ConversationManager()
                .withProvider("local")
                .withModel((String) ConfigManager.getSetting("AIModel"))
                .withEndpoint((String) ConfigManager.getSetting("AIEndpoint"))
                .withTemperature((Double) ConfigManager.getSetting("AITemperature"))
                .withMaxTokens(maxTokens)
                .withToolsEnabled(ConfigManager.getFeature("AITools"))
                .withSystemPrompt((String) ConfigManager.getSetting("AISystemPrompt"));
    }
    // Timeouts
    private int connectTimeoutSec = 10;
    private int readTimeoutSec = 60;

    public ConversationManager withConnectTimeoutSec(int sec) { this.connectTimeoutSec = Math.max(1, sec); return this; }
    public ConversationManager withReadTimeoutSec(int sec) { this.readTimeoutSec = Math.max(1, sec); return this; }

    // Conversation helpers
    public ConversationManager system(String content) { conversation.system(content); return this; }
    public void addUserMessage(String content) {conversation.user(content);}
    public List<AIMessage> getHistory() { return Collections.unmodifiableList(conversation.getMessages()); }
    public void clear() { conversation.clear(); }

    /** Send a user message and get a non-streaming response. */
    public CompletableFuture<String> sendMessage(String userMessage) {
        try {
            if (userMessage != null) conversation.user(userMessage);
            if (isLocal()) {
                return sendViaLocalWithTools();
            }
            throw new UnsupportedOperationException("Provider not implemented: " + provider);
        } catch (RuntimeException e) {
            try { ErrorReporter.reportError("ConversationManager sendMessage failed", e.getMessage()); } catch (Exception ignored) {}
            throw e;
        }
    }

    private boolean isLocal() { return provider == null || provider.equalsIgnoreCase("local"); }

    private CompletableFuture<String> sendViaLocalWithTools() {
        if (!enableTools || ToolRegistry.getRegisteredToolNames().isEmpty()) {
            return sendViaLocal();
        }

        // Use the enhanced tool calling manager for proper tool handling
        ToolCallingConversationManager toolManager = new ToolCallingConversationManager()
                .withProvider(provider)
                .withModel(model)
                .withEndpoint(endpoint)
                .withTemperature(temperature)
                .withMaxTokens(maxTokens)
                .withConnectTimeoutSec(connectTimeoutSec)
                .withReadTimeoutSec(readTimeoutSec)
                .withToolsEnabled(enableTools);

        // Copy conversation history
        for (AIMessage msg : conversation.getMessages()) {
            toolManager.conversation.add(msg);
        }

        return toolManager.sendWithToolHandling().thenApply(response -> {
            // Copy back the conversation history
            conversation.clear();
            for (AIMessage msg : toolManager.conversation.getMessages()) {
                conversation.add(msg);
            }
            return response;
        });
    }

    private CompletableFuture<String> sendViaLocal() {
        Local client = new Local()
                .withModel(model)
                .withTemperature(temperature)
                .withMaxTokens(maxTokens)
                .withEndpoint(endpoint)
                .withConnectTimeoutSec(connectTimeoutSec)
                .withReadTimeoutSec(readTimeoutSec);
        // Convert Conversation -> Local history
        List<Local.Message> localHistory = new ArrayList<>();
        for (AIMessage m : conversation.getMessages()) localHistory.add(new Local.Message(m.role, m.content));
        client.withHistory(localHistory);
        int before = localHistory.size();

        return client.complete().thenApply(response -> {
            List<Local.Message> afterHist = client.getHistory();
            for (int i = before; i < afterHist.size(); i++) {
                Local.Message m = afterHist.get(i);
                conversation.add(m.role, m.content);
            }
            return response;
        });
    }
}

