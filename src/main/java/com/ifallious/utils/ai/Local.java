package com.ifallious.utils.ai;

import com.google.gson.Gson;
import com.ifallious.utils.ErrorReporter;
import com.ifallious.Wynnutils;
import com.ifallious.utils.ai.core.Conversation;
import com.ifallious.utils.ai.json.ChatJson;
import com.ifallious.utils.ai.json.ToolCallParser;
import com.ifallious.utils.ai.model.AIMessage;
import com.ifallious.utils.ai.model.ChatRequest;
import com.ifallious.utils.ai.model.ChatResponse;
import com.ifallious.utils.ai.tools.ToolRegistry;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import okhttp3.*;

/**
 * OpenAI-compatible client targeting the local server at 127.0.0.1:1234.
 * This class focuses on HTTP calls and high-level flow, delegating JSON parsing,
 * conversation handling, and streaming helpers to dedicated classes.
 */
public class Local {
    private static final String DEFAULT_ENDPOINT = "http://127.0.0.1:1234/v1/chat/completions";
    private static final Gson GSON = new Gson();

    // Config
    private String endpoint = DEFAULT_ENDPOINT;
    private String model = "openai/gpt-oss-20b";
    private double temperature = 0.7;
    private int maxTokens = -1;
    private int connectTimeoutSec = 10;
    private int readTimeoutSec = 60;
    private boolean enableTools = false;

    OkHttpClient client = new OkHttpClient.Builder()
            .callTimeout(5, TimeUnit.MINUTES) // total time for the call
            .readTimeout(5, TimeUnit.MINUTES)
            .build();


    // State
    private final Conversation conversation = new Conversation();

    // Backwards-compatible listener
    public interface StreamListener {
        void onDelta(String delta);
        default void onComplete(String fullContent) {}
        default void onError(Exception e) {}
    }

    // Backwards-compatible message struct (adapter to AIMessage)
    public static class Message {
        public String role;
        public String content;
        public String tool_call_id; // for tool role messages
        public Message() {}
        public Message(String role, String content) { this.role = role; this.content = content; }
    }

    public Local withConnectTimeoutSec(int seconds) { this.connectTimeoutSec = Math.max(1, seconds); this.client = null; return this; }
    public Local withReadTimeoutSec(int seconds) { this.readTimeoutSec = Math.max(1, seconds); return this; }

    public Local() {}

    // Fluent setters
    public Local withEndpoint(String endpoint) { if (endpoint != null && !endpoint.isBlank()) this.endpoint = endpoint; return this; }
    public Local withModel(String model) { if (model != null && !model.isBlank()) this.model = model; return this; }
    public Local withTemperature(double temperature) { this.temperature = Math.max(0.0, Math.min(1.0, temperature)); return this; }
    public Local withMaxTokens(int maxTokens) { this.maxTokens = maxTokens; return this; }
    public Local withToolsEnabled(boolean enabled) { this.enableTools = enabled; return this; }

    // History management (adapts to Conversation)
    public Local addSystem(String content) { conversation.system(content); return this; }
    public Local addUser(String content) { conversation.user(content); return this; }
    public Local addAssistant(String content) { conversation.assistant(content); return this; }
    public Local add(String role, String content) { conversation.add(role, content); return this; }
    public List<Message> getHistory() {
        List<Message> list = new ArrayList<>();
        for (AIMessage m : conversation.getMessages()) list.add(new Message(m.role, m.content));
        return Collections.unmodifiableList(list);
    }
    public void clearHistory() { conversation.clear(); }
    public Local withHistory(List<Message> messages) {
        conversation.clear();
        if (messages != null) {
            for (Message m : messages) {
                AIMessage msg = new AIMessage(m.role, m.content);
                if (m.tool_call_id != null) {
                    msg.tool_call_id = m.tool_call_id;
                }
                conversation.add(msg);
            }
        }
        return this;
    }

    // Logging helpers
    private void logInfo(String msg) { Wynnutils.LOGGER.info("[LocalAI] " + msg); }
    private void logWarn(String msg) { Wynnutils.LOGGER.warn("[LocalAI] " + msg); }
    private void logError(String msg, Throwable t) { Wynnutils.LOGGER.error("[LocalAI] " + msg, t); }

    public CompletableFuture<String> complete() {
        try {
            return sendRequest(buildRequest()).thenApply(ChatJson::extractContent);
        } catch (Exception e) {
            try { ErrorReporter.reportError("Local AI request failed", e.getMessage()); } catch (Exception ignored) {}
            throw toAIClientException(e);
        }
    }

    /**
     * Complete with full response parsing (including tool calls)
     */
    public CompletableFuture<ToolCallParser.ParsedResponse> completeWithToolCalls() {
        try {
            return sendRequest(buildRequest()).thenApply(ToolCallParser::parseResponse);
        } catch (Exception e) {
            try { ErrorReporter.reportError("Local AI request failed", e.getMessage()); } catch (Exception ignored) {}
            throw toAIClientException(e);
        }
    }

    private static final MediaType JSON = MediaType.get("application/json; charset=utf-8");

    private CompletableFuture<String> sendRequest(ChatRequest req) throws IOException {
        if (client == null) {client = new OkHttpClient();}
        CompletableFuture<String> future = new CompletableFuture<>();
        final String[] responseJSON = {null};
        String payload = GSON.toJson(req);
        logInfo("POST " + endpoint + " (non-stream), model=" + req.model + ", msgs=" + (req.messages==null?0:req.messages.size()));
        RequestBody body = RequestBody.create(
                payload,
                MediaType.parse("application/json")
        );

        Request request = new Request.Builder()
                .url(endpoint)
                .post(body)
                .build();

        // Async request
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                future.completeExceptionally(e);
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                try (response) {
                    future.complete(response.body().string());
                }
            }
        });
        return future;
    }

    private ChatRequest buildRequest() {
        ChatRequest req = new ChatRequest();
        req.model = this.model;
        req.messages = new ArrayList<>(conversation.getMessages());
        req.temperature = this.temperature;
        req.max_tokens = this.maxTokens;
        req.stream = false;

        // Add tools if enabled and available
        if (enableTools && !ToolRegistry.getRegisteredToolNames().isEmpty()) {
            req.tools = ToolRegistry.getToolsForAI();
        }

        return req;
    }

    private static String safeBody(String body) {
        if (body == null) return "";
        body = body.trim();
        if (body.length() > 400) body = body.substring(0, 400) + "...";
        return body;
    }

    // Custom unchecked exception for client errors
    public static class AIClientException extends RuntimeException {
        public AIClientException(String message) { super(message); }
        public AIClientException(String message, Throwable cause) { super(message, cause); }
    }

    private static AIClientException toAIClientException(Exception e) {
        if (e instanceof AIClientException) return (AIClientException) e;
        return new AIClientException(e.getMessage(), e);
    }
}
