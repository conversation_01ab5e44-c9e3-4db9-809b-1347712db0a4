package com.ifallious.utils.ai.tools;

import com.ifallious.utils.ai.tools.impl.*;
import com.ifallious.Wynnutils;

/**
 * Manager class for initializing and registering all available tools.
 * Add new tools here to make them available to the AI.
 */
public class ToolManager {
    private static boolean initialized = false;

    /**
     * Initialize and register all available tools.
     * This should be called during mod initialization.
     */
    public static void initializeTools() {
        if (initialized) {
            return;
        }

        try {
            // Register built-in tools
            ToolRegistry.registerTool(new WikipediaTool());
            ToolRegistry.registerTool(new MinecraftInfoTool());
            ToolRegistry.registerTool(new CalculatorTool());
            ToolRegistry.registerTool(new WynncraftPlayerAPI());
            ToolRegistry.registerTool(new WynncraftGuildAPI());
            ToolRegistry.registerTool(new RaidAspectTool());

            // Add more tools here as needed
            // ToolRegistry.registerTool(new YourCustomTool());

            initialized = true;
            Wynnutils.LOGGER.info("AI Tools initialized successfully. Registered tools: " + 
                    ToolRegistry.getRegisteredToolNames());

        } catch (Exception e) {
            Wynnutils.LOGGER.error("Failed to initialize AI tools", e);
        }
    }

    /**
     * Check if tools have been initialized
     * @return true if tools are initialized
     */
    public static boolean isInitialized() {
        return initialized;
    }

    /**
     * Get the number of registered tools
     * @return number of registered tools
     */
    public static int getToolCount() {
        return ToolRegistry.getRegisteredToolNames().size();
    }
}
