package com.ifallious.utils.ai.tools;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation to define parameters for tool methods.
 * Used to automatically generate OpenAI-compatible tool definitions.
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.PARAMETER)
public @interface ToolParameter {
    /**
     * Name of the parameter (defaults to parameter name if empty)
     */
    String name() default "";
    
    /**
     * Type of the parameter for OpenAI (string, number, boolean, object, array)
     */
    String type();
    
    /**
     * Description of what this parameter does
     */
    String description();
    
    /**
     * Whether this parameter is required
     */
    boolean required() default true;
}
