package com.ifallious.utils.ai.tools.impl;

import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.ifallious.utils.ai.tools.*;
import gg.essential.universal.UChat;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class WynncraftPlayerAPI implements Tool {
    private static final Gson GSON = new Gson();

    @Override
    public String getName() {
        return "get_wynncraft_player";
    }

    @Override
    public String getDescription() {
        return """
                Gets information about a Player on Wynncraft from the API. You can specify exactly what information you want to retrieve.

                Available information fields:
                - "username", "online", "server", "activeCharacter", "uuid"
                - "rank", "rankBadge", "legacyRankColour", "shortenedRank", "supportRank", "veteran" (note rank refers to regular player or admininistrator supportRank is the actual rank the player has)
                - "firstJoin", "lastJoin", "playtime"
                - "guild" (includes name, prefix, rank, rankStars)
                - "globalData" (includes wars, totalLevels, killedMobs, chestsFound, dungeons, raids, completedQuests, pvp)
                - "forumLink", "ranking", "previousRanking", "publicProfile"
                - "characters" (detailed character information)

                You can request multiple fields by separating them with commas, or use "all" to get everything.
                Examples:
                - "username,online,server" - Get basic online status
                - "guild,rank" - Get guild and rank info
                - "globalData.wars,globalData.totalLevels" - Get specific global stats
                - "characters" - Get all character data
                - "all" - Get complete player information
                """;
    }

    @ToolExecute
    public String execute(
            @ToolParameter(
                    name = "username",
                    type = "string",
                    description = "The Username to query the API with",
                    required = true
            ) String username,

            @ToolParameter(
                    name = "requested_info",
                    type = "string",
                    description = "Specific information to retrieve. Use comma-separated field names, dot notation for nested fields (e.g., 'globalData.wars'), or 'all' for everything",
                    required = true
            ) String requestedInfo
    ) throws IOException {
        try {
            // Get full API response
            String url = "https://api.wynncraft.com/v3/player/" + username + "?fullResult";
            OkHttpClient client = new OkHttpClient();
            Request request = new Request.Builder()
                    .url(url)
                    .build();
            Response response = client.newCall(request).execute();
            String fullResponse = response.body().string();

            // If no specific info requested or "all" requested, return full response
            if (requestedInfo == null || requestedInfo.trim().isEmpty() || "all".equalsIgnoreCase(requestedInfo.trim())) {
                return fullResponse;
            }

            // Parse the response and filter for requested information
            return filterResponse(fullResponse, requestedInfo);

        } catch (Exception e) {
            return createErrorResult("Failed to fetch player data: " + e.getMessage());
        }
    }

    private String filterResponse(String fullResponse, String requestedInfo) {
        try {
            JsonObject fullData = JsonParser.parseString(fullResponse).getAsJsonObject();
            JsonObject filteredData = new JsonObject();

            // Split requested fields by comma
            String[] fields = requestedInfo.split(",");

            for (String field : fields) {
                field = field.trim();
                extractField(fullData, filteredData, field);
            }

            return GSON.toJson(filteredData);

        } catch (Exception e) {
            return createErrorResult("Failed to filter response: " + e.getMessage());
        }
    }

    private void extractField(JsonObject source, JsonObject target, String fieldPath) {
        String[] parts = fieldPath.split("\\.");
        JsonObject currentSource = source;
        JsonObject currentTarget = target;

        // Navigate to the parent of the final field
        for (int i = 0; i < parts.length - 1; i++) {
            String part = parts[i];
            if (currentSource.has(part) && currentSource.get(part).isJsonObject()) {
                currentSource = currentSource.getAsJsonObject(part);

                // Create nested structure in target if it doesn't exist
                if (!currentTarget.has(part)) {
                    currentTarget.add(part, new JsonObject());
                }
                currentTarget = currentTarget.getAsJsonObject(part);
            } else {
                // Path doesn't exist in source
                return;
            }
        }

        // Extract the final field
        String finalField = parts[parts.length - 1];
        if (currentSource.has(finalField)) {
            currentTarget.add(finalField, currentSource.get(finalField));
        }
    }

    private String createErrorResult(String message) {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "error");
        result.put("message", message);
        return GSON.toJson(result);
    }
}
