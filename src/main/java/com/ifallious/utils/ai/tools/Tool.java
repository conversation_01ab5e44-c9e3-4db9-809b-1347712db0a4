package com.ifallious.utils.ai.tools;

/**
 * Base interface for all AI tools.
 * Tools must implement this interface and have a method annotated with @ToolExecute.
 */
public interface Tool {
    /**
     * Get the name of the tool (used by AI to call it)
     * @return Tool name
     */
    String getName();
    
    /**
     * Get the description of what this tool does
     * @return Tool description for AI understanding
     */
    String getDescription();
}
