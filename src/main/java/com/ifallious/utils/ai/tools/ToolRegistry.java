package com.ifallious.utils.ai.tools;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.ifallious.Wynnutils;
import com.ifallious.utils.ErrorReporter;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Central registry for AI tools that can be called by the AI model.
 * Provides a framework for easy addition of new tools with automatic parameter handling.
 */
public class ToolRegistry {
    private static final Gson GSON = new Gson();
    private static final Map<String, ToolDefinition> tools = new ConcurrentHashMap<>();
    
    /**
     * Register a tool with the registry
     * @param tool The tool to register
     */
    public static void registerTool(Tool tool) {
        ToolDefinition definition = createToolDefinition(tool);
        tools.put(tool.getName(), definition);
        Wynnutils.LOGGER.info("Registered tool: " + tool.getName());
    }
    
    /**
     * Get all registered tools in OpenAI format
     * @return List of tool definitions for AI model
     */
    public static List<Map<String, Object>> getToolsForAI() {
        List<Map<String, Object>> toolList = new ArrayList<>();
        for (ToolDefinition def : tools.values()) {
            toolList.add(def.toOpenAIFormat());
        }
        return toolList;
    }
    
    /**
     * Execute a tool call from the AI
     * @param toolName Name of the tool to execute
     * @param arguments JSON arguments from the AI
     * @return CompletableFuture with the tool execution result
     */
    public static CompletableFuture<String> executeTool(String toolName, String arguments) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                ToolDefinition tool = tools.get(toolName);
                if (tool == null) {
                    return createErrorResult("Tool not found: " + toolName);
                }
                
                JsonObject args = GSON.fromJson(arguments, JsonObject.class);
                return tool.execute(args);
                
            } catch (Exception e) {
                Wynnutils.LOGGER.error("Error executing tool: " + toolName, e);
                ErrorReporter.reportError("Tool execution failed: " + toolName, e.getMessage());
                return createErrorResult("Tool execution failed: " + e.getMessage());
            }
        });
    }
    
    /**
     * Get a list of all registered tool names
     * @return Set of tool names
     */
    public static Set<String> getRegisteredToolNames() {
        return new HashSet<>(tools.keySet());
    }
    
    /**
     * Check if a tool is registered
     * @param toolName Name of the tool
     * @return true if the tool is registered
     */
    public static boolean isToolRegistered(String toolName) {
        return tools.containsKey(toolName);
    }
    
    private static ToolDefinition createToolDefinition(Tool tool) {
        return new ToolDefinition(tool);
    }
    
    private static String createErrorResult(String message) {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "error");
        result.put("message", message);
        return GSON.toJson(result);
    }
    
    /**
     * Internal class representing a tool definition with execution capability
     */
    private static class ToolDefinition {
        private final Tool tool;
        private final Method executeMethod;
        private final Map<String, ParameterInfo> parameters;
        
        public ToolDefinition(Tool tool) {
            this.tool = tool;
            this.executeMethod = findExecuteMethod(tool);
            this.parameters = extractParameters(executeMethod);
        }
        
        public String execute(JsonObject args) throws Exception {
            Object[] methodArgs = prepareMethodArguments(args);
            Object result = executeMethod.invoke(tool, methodArgs);
            
            if (result instanceof String) {
                return (String) result;
            } else {
                return GSON.toJson(result);
            }
        }
        
        public Map<String, Object> toOpenAIFormat() {
            Map<String, Object> toolDef = new HashMap<>();
            toolDef.put("type", "function");
            
            Map<String, Object> function = new HashMap<>();
            function.put("name", tool.getName());
            function.put("description", tool.getDescription());
            
            Map<String, Object> params = new HashMap<>();
            params.put("type", "object");
            
            Map<String, Object> properties = new HashMap<>();
            List<String> required = new ArrayList<>();
            
            for (Map.Entry<String, ParameterInfo> entry : parameters.entrySet()) {
                ParameterInfo param = entry.getValue();
                Map<String, Object> paramDef = new HashMap<>();
                paramDef.put("type", param.type);
                paramDef.put("description", param.description);
                properties.put(entry.getKey(), paramDef);
                
                if (param.required) {
                    required.add(entry.getKey());
                }
            }
            
            params.put("properties", properties);
            params.put("required", required);
            function.put("parameters", params);
            
            toolDef.put("function", function);
            return toolDef;
        }
        
        private Method findExecuteMethod(Tool tool) {
            for (Method method : tool.getClass().getMethods()) {
                if (method.isAnnotationPresent(ToolExecute.class)) {
                    return method;
                }
            }
            throw new IllegalArgumentException("Tool must have a method annotated with @ToolExecute");
        }
        
        private Map<String, ParameterInfo> extractParameters(Method method) {
            Map<String, ParameterInfo> params = new LinkedHashMap<>();
            
            for (Parameter param : method.getParameters()) {
                ToolParameter annotation = param.getAnnotation(ToolParameter.class);
                if (annotation != null) {
                    String name = annotation.name().isEmpty() ? param.getName() : annotation.name();
                    params.put(name, new ParameterInfo(
                        annotation.type(),
                        annotation.description(),
                        annotation.required(),
                        param.getType()
                    ));
                }
            }
            
            return params;
        }
        
        private Object[] prepareMethodArguments(JsonObject args) {
            Object[] methodArgs = new Object[parameters.size()];
            int index = 0;
            
            for (Map.Entry<String, ParameterInfo> entry : parameters.entrySet()) {
                String paramName = entry.getKey();
                ParameterInfo paramInfo = entry.getValue();
                
                if (args.has(paramName)) {
                    methodArgs[index] = convertJsonToJava(args.get(paramName), paramInfo.javaType);
                } else if (paramInfo.required) {
                    throw new IllegalArgumentException("Required parameter missing: " + paramName);
                } else {
                    methodArgs[index] = getDefaultValue(paramInfo.javaType);
                }
                index++;
            }
            
            return methodArgs;
        }
        
        private Object convertJsonToJava(com.google.gson.JsonElement element, Class<?> targetType) {
            if (element.isJsonNull()) return null;
            
            if (targetType == String.class) {
                return element.getAsString();
            } else if (targetType == int.class || targetType == Integer.class) {
                return element.getAsInt();
            } else if (targetType == double.class || targetType == Double.class) {
                return element.getAsDouble();
            } else if (targetType == boolean.class || targetType == Boolean.class) {
                return element.getAsBoolean();
            } else if (targetType == long.class || targetType == Long.class) {
                return element.getAsLong();
            } else {
                return GSON.fromJson(element, targetType);
            }
        }
        
        private Object getDefaultValue(Class<?> type) {
            if (type == boolean.class) return false;
            if (type == int.class) return 0;
            if (type == double.class) return 0.0;
            if (type == long.class) return 0L;
            return null;
        }
    }
    
    private static class ParameterInfo {
        final String type;
        final String description;
        final boolean required;
        final Class<?> javaType;
        
        ParameterInfo(String type, String description, boolean required, Class<?> javaType) {
            this.type = type;
            this.description = description;
            this.required = required;
            this.javaType = javaType;
        }
    }
}
