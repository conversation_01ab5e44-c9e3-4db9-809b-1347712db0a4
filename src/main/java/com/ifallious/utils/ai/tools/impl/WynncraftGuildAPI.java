package com.ifallious.utils.ai.tools.impl;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.ifallious.utils.ai.tools.*;
import gg.essential.universal.UChat;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class WynncraftGuildAPI implements Tool {
    private static final Gson GSON = new Gson();

    @Override
    public String getName() {
        return "get_wynncraft_guild";
    }

    @Override
    public String getDescription() {
        return """
                Gets information about a Guild on Wynncraft from the API. You can specify exactly what information you want to retrieve.
                
                The tool automatically determines whether to use the full guild name endpoint or the prefix endpoint based on the input:
                - If the input is 3-4 characters, it's treated as a guild prefix
                - Otherwise, it's treated as a full guild name
                
                Available information fields:
                - "uuid", "name", "prefix", "level", "xpPercent"
                - "territories", "wars", "created", "online"
                - "members" (includes total count and detailed member information by rank)
                - "members.total" - Just the total member count
                - "members.owner", "members.chief", "members.strategist", "members.captain", "members.recruiter", "members.recruit" - Members by rank
                - "banner" (includes base, tier, structure, layers with colors and patterns)
                - "seasonRanks" (historical season performance data)
                
                You can request multiple fields by separating them with commas, or use "all" to get everything.
                Examples:
                - "name,prefix,level" - Get basic guild info
                - "members.total,territories,wars" - Get member count and activity stats
                - "members.owner,members.chief" - Get leadership information
                - "banner" - Get guild banner design
                - "all" - Get complete guild information
                """;
    }

    @ToolExecute
    public String execute(
            @ToolParameter(
                    name = "guild_identifier",
                    type = "string", 
                    description = "The guild name or prefix to query. If 3-4 uppercase characters, treated as prefix; otherwise as full name",
                    required = true
            ) String guildIdentifier,

            @ToolParameter(
                    name = "requested_info",
                    type = "string",
                    description = "Specific information to retrieve. Use comma-separated field names, dot notation for nested fields (e.g., 'members.total'), or 'all' for everything",
                    required = true
            ) String requestedInfo
    ) throws IOException {
        try {
            // Determine if this is a prefix or full name
            boolean isPrefix = isGuildPrefix(guildIdentifier);
            
            // Build the appropriate URL
            String url;
            if (isPrefix) {
                url = "https://api.wynncraft.com/v3/guild/prefix/" + guildIdentifier + "?identifier=username";
            } else {
                url = "https://api.wynncraft.com/v3/guild/" + guildIdentifier + "?identifier=username";
            }
            
            // Make the API request
            OkHttpClient client = new OkHttpClient();
            Request request = new Request.Builder()
                    .url(url)
                    .build();
            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                return createErrorResult("Failed to retrieve guild information from Wynncraft API. Likely due to the user misspelling the guild Ask them to provide the Tag with correct capitalization");
            }
            String fullResponse = response.body().string();

            // If no specific info requested or "all" requested, return full response
            if (requestedInfo == null || requestedInfo.trim().isEmpty() || "all".equalsIgnoreCase(requestedInfo.trim())) {
                return fullResponse;
            }

            // Parse the response and filter for requested information
            UChat.chat(fullResponse);
            return filterResponse(fullResponse, requestedInfo);

        } catch (Exception e) {
            return createErrorResult("Failed to fetch guild data: " + e.getMessage());
        }
    }

    /**
     * Determines if the input should be treated as a guild prefix
     * @param identifier The guild identifier
     * @return true if it should be treated as a prefix, false if as a full name
     */
    private boolean isGuildPrefix(String identifier) {
        if (identifier == null || identifier.trim().isEmpty()) {
            return false;
        }
        
        String trimmed = identifier.trim();
        // Check if it's 3-4 characters and all uppercase
        return trimmed.length() >= 3 && trimmed.length() <= 4;
    }

    private String filterResponse(String fullResponse, String requestedInfo) {
        try {
            JsonObject fullData = JsonParser.parseString(fullResponse).getAsJsonObject();
            JsonObject filteredData = new JsonObject();

            // Split requested fields by comma
            String[] fields = requestedInfo.split(",");

            for (String field : fields) {
                field = field.trim();
                extractField(fullData, filteredData, field);
            }

            return GSON.toJson(filteredData);

        } catch (Exception e) {
            return createErrorResult("Failed to filter response: " + e.getMessage());
        }
    }

    private void extractField(JsonObject source, JsonObject target, String fieldPath) {
        String[] parts = fieldPath.split("\\.");
        JsonObject currentSource = source;
        JsonObject currentTarget = target;

        // Navigate to the parent of the final field
        for (int i = 0; i < parts.length - 1; i++) {
            String part = parts[i];
            if (currentSource.has(part) && currentSource.get(part).isJsonObject()) {
                currentSource = currentSource.getAsJsonObject(part);

                // Create nested structure in target if it doesn't exist
                if (!currentTarget.has(part)) {
                    currentTarget.add(part, new JsonObject());
                }
                currentTarget = currentTarget.getAsJsonObject(part);
            } else {
                // Path doesn't exist in source
                return;
            }
        }

        // Extract the final field
        String finalField = parts[parts.length - 1];
        if (currentSource.has(finalField)) {
            currentTarget.add(finalField, currentSource.get(finalField));
        }
    }

    private String createErrorResult(String message) {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "error");
        result.put("message", message);
        return GSON.toJson(result);
    }
}
