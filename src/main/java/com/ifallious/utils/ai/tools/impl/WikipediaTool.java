package com.ifallious.utils.ai.tools.impl;

import com.google.gson.Gson;
import com.ifallious.utils.ai.tools.Tool;
import com.ifallious.utils.ai.tools.ToolExecute;
import com.ifallious.utils.ai.tools.ToolParameter;
import com.ifallious.Wynnutils;

import java.io.IOException;
import java.net.URI;
import java.net.URLEncoder;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * Tool for fetching Wikipedia content, similar to the Python example.
 * Demonstrates how to create a tool that makes HTTP requests and returns JSON.
 */
public class WikipediaTool implements Tool {
    private static final Gson GSON = new Gson();
    private static final HttpClient HTTP_CLIENT = HttpClient.newBuilder()
            .connectTimeout(Duration.ofSeconds(10))
            .build();

    @Override
    public String getName() {
        return "fetch_wikipedia_content";
    }

    @Override
    public String getDescription() {
        return "Search Wikipedia and fetch the introduction of the most relevant article. " +
               "Always use this if the user is asking for something that is likely on wikipedia. " +
               "If the user has a typo in their search query, correct it before searching.";
    }

    @ToolExecute
    public String execute(
            @ToolParameter(
                    name = "search_query",
                    type = "string",
                    description = "Search query for finding the Wikipedia article",
                    required = true
            ) String searchQuery
    ) {
        try {
            return fetchWikipediaContent(searchQuery);
        } catch (Exception e) {
            Wynnutils.LOGGER.error("Error fetching Wikipedia content", e);
            return createErrorResult("Failed to fetch Wikipedia content: " + e.getMessage());
        }
    }

    private String fetchWikipediaContent(String searchQuery) throws IOException, InterruptedException {
        try {
            // First, search for the most relevant article
            String searchUrl = "https://en.wikipedia.org/w/api.php?action=query&format=json&list=search&srsearch=" +
                    URLEncoder.encode(searchQuery, StandardCharsets.UTF_8) + "&srlimit=1";

            HttpRequest searchRequest = HttpRequest.newBuilder()
                    .uri(URI.create(searchUrl))
                    .timeout(Duration.ofSeconds(30))
                    .GET()
                    .build();

            HttpResponse<String> searchResponse = HTTP_CLIENT.send(searchRequest, HttpResponse.BodyHandlers.ofString());

            if (searchResponse.statusCode() != 200) {
                return createErrorResult("Wikipedia search failed with status: " + searchResponse.statusCode());
            }

            // Parse search results
            Map<String, Object> searchData = GSON.fromJson(searchResponse.body(), Map.class);
            Map<String, Object> query = (Map<String, Object>) searchData.get("query");
            
            if (query == null) {
                return createErrorResult("Invalid Wikipedia search response");
            }
            
            java.util.List<Map<String, Object>> searchResults = (java.util.List<Map<String, Object>>) query.get("search");
            
            if (searchResults == null || searchResults.isEmpty()) {
                return createErrorResult("No Wikipedia article found for '" + searchQuery + "'");
            }

            String normalizedTitle = (String) searchResults.get(0).get("title");

            // Now fetch the actual content
            String contentUrl = "https://en.wikipedia.org/w/api.php?action=query&format=json&titles=" +
                    URLEncoder.encode(normalizedTitle, StandardCharsets.UTF_8) +
                    "&prop=extracts&exintro=true&explaintext=true&redirects=1";

            HttpRequest contentRequest = HttpRequest.newBuilder()
                    .uri(URI.create(contentUrl))
                    .timeout(Duration.ofSeconds(30))
                    .GET()
                    .build();

            HttpResponse<String> contentResponse = HTTP_CLIENT.send(contentRequest, HttpResponse.BodyHandlers.ofString());

            if (contentResponse.statusCode() != 200) {
                return createErrorResult("Wikipedia content fetch failed with status: " + contentResponse.statusCode());
            }

            // Parse content response
            Map<String, Object> contentData = GSON.fromJson(contentResponse.body(), Map.class);
            Map<String, Object> contentQuery = (Map<String, Object>) contentData.get("query");
            
            if (contentQuery == null) {
                return createErrorResult("Invalid Wikipedia content response");
            }
            
            Map<String, Object> pages = (Map<String, Object>) contentQuery.get("pages");
            
            if (pages == null || pages.isEmpty()) {
                return createErrorResult("No Wikipedia article found for '" + searchQuery + "'");
            }

            // Get the first (and should be only) page
            Map<String, Object> page = (Map<String, Object>) pages.values().iterator().next();
            
            if (page.containsKey("missing")) {
                return createErrorResult("No Wikipedia article found for '" + searchQuery + "'");
            }

            String content = (String) page.get("extract");
            String title = (String) page.get("title");

            if (content == null || content.trim().isEmpty()) {
                return createErrorResult("Wikipedia article found but has no content");
            }

            // Create success result
            Map<String, Object> result = new HashMap<>();
            result.put("status", "success");
            result.put("content", content.trim());
            result.put("title", title);

            return GSON.toJson(result);

        } catch (Exception e) {
            Wynnutils.LOGGER.error("Exception in Wikipedia fetch", e);
            return createErrorResult("Exception occurred: " + e.getMessage());
        }
    }

    private String createErrorResult(String message) {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "error");
        result.put("message", message);
        return GSON.toJson(result);
    }
}
