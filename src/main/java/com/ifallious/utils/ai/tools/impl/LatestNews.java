package com.ifallious.utils.ai.tools.impl;

import com.google.gson.Gson;
import com.google.gson.JsonParser;
import com.ifallious.utils.ai.tools.*;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class LatestNews implements Tool {
    private static final Gson GSON = new Gson();

    @Override
    public String getName() {
        return "get_wynncraft_news";
    }
    @Override
    public String getDescription() {
        return """
                Gets the latest news from Wynncraft.
                Includes multiple objects with
                "title", "date", "forumThread", "author", "content", "comments"
                """;
    }

    @ToolExecute
    public String execute(
    ) throws IOException {
        try {
            String url = "https://api.wynncraft.com/v3/latest-news";
            OkHttpClient client = new OkHttpClient();
            Request request = new Request.Builder()
                    .url(url)
                    .build();
            Response response = client.newCall(request).execute();
            return JsonParser.parseString(response.body().string()).getAsString();
        } catch (Exception e) {
            return createErrorResult("Failed to fetch latest news: " + e.getMessage());
        }
    }
    private String createErrorResult(String message) {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "error");
        result.put("message", message);
        return GSON.toJson(result);
    }
}
