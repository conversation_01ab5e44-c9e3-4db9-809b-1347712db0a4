package com.ifallious.utils.ai.tools.impl;

import com.google.gson.Gson;
import com.ifallious.utils.ai.tools.Tool;
import com.ifallious.utils.ai.tools.ToolExecute;
import com.ifallious.utils.ai.tools.ToolParameter;

import java.util.HashMap;
import java.util.Map;

/**
 * Simple calculator tool to demonstrate the framework.
 * Shows how to create a tool with multiple parameters and basic computation.
 */
public class CalculatorTool implements Tool {
    private static final Gson GSON = new Gson();

    @Override
    public String getName() {
        return "calculate";
    }

    @Override
    public String getDescription() {
        return "Perform basic mathematical calculations. Supports addition, subtraction, " +
               "multiplication, and division of two numbers.";
    }

    @ToolExecute
    public String execute(
            @ToolParameter(
                    name = "operation",
                    type = "string",
                    description = "The mathematical operation to perform: 'add', 'subtract', 'multiply', or 'divide'",
                    required = true
            ) String operation,
            
            @ToolParameter(
                    name = "a",
                    type = "number",
                    description = "The first number",
                    required = true
            ) double a,
            
            @ToolParameter(
                    name = "b",
                    type = "number",
                    description = "The second number",
                    required = true
            ) double b
    ) {
        try {
            double result;
            
            switch (operation.toLowerCase()) {
                case "add":
                case "addition":
                case "+":
                    result = a + b;
                    break;
                case "subtract":
                case "subtraction":
                case "-":
                    result = a - b;
                    break;
                case "multiply":
                case "multiplication":
                case "*":
                    result = a * b;
                    break;
                case "divide":
                case "division":
                case "/":
                    if (b == 0) {
                        return createErrorResult("Cannot divide by zero");
                    }
                    result = a / b;
                    break;
                default:
                    return createErrorResult("Unknown operation: " + operation + 
                            ". Supported operations: add, subtract, multiply, divide");
            }

            return createSuccessResult(result, operation, a, b);

        } catch (Exception e) {
            return createErrorResult("Calculation failed: " + e.getMessage());
        }
    }

    private String createSuccessResult(double result, String operation, double a, double b) {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "success");
        response.put("result", result);
        response.put("operation", operation);
        response.put("operands", Map.of("a", a, "b", b));
        response.put("expression", formatExpression(operation, a, b, result));
        return GSON.toJson(response);
    }

    private String createErrorResult(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "error");
        response.put("message", message);
        return GSON.toJson(response);
    }

    private String formatExpression(String operation, double a, double b, double result) {
        String operator;
        switch (operation.toLowerCase()) {
            case "add":
            case "addition":
                operator = "+";
                break;
            case "subtract":
            case "subtraction":
                operator = "-";
                break;
            case "multiply":
            case "multiplication":
                operator = "*";
                break;
            case "divide":
            case "division":
                operator = "/";
                break;
            default:
                operator = operation;
        }
        
        return String.format("%.2f %s %.2f = %.2f", a, operator, b, result);
    }
}
