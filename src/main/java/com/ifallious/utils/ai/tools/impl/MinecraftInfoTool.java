package com.ifallious.utils.ai.tools.impl;

import com.google.gson.Gson;
import com.ifallious.utils.ai.tools.Tool;
import com.ifallious.utils.ai.tools.ToolExecute;
import com.ifallious.utils.ai.tools.ToolParameter;
import com.ifallious.Wynnutils;
import net.minecraft.client.MinecraftClient;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.util.math.BlockPos;

import java.util.HashMap;
import java.util.Map;

/**
 * Tool for getting Minecraft game information.
 * Demonstrates how to create a tool that accesses game state.
 */
public class MinecraftInfoTool implements Tool {
    private static final Gson GSON = new Gson();

    @Override
    public String getName() {
        return "get_minecraft_info";
    }

    @Override
    public String getDescription() {
        return "Get information about the current Minecraft game state, including player position, " +
               "health, dimension, and other game details.";
    }

    @ToolExecute
    public String execute(
            @ToolParameter(
                    name = "info_type",
                    type = "string",
                    description = "Type of information to get: 'position', 'health', 'dimension', 'all'",
                    required = true
            ) String infoType
    ) {
        try {
            MinecraftClient mc = MinecraftClient.getInstance();
            
            if (mc.player == null || mc.world == null) {
                return createErrorResult("Player is not in a world");
            }

            Map<String, Object> result = new HashMap<>();
            result.put("status", "success");

            switch (infoType.toLowerCase()) {
                case "position":
                    result.put("data", getPositionInfo(mc.player));
                    break;
                case "health":
                    result.put("data", getHealthInfo(mc.player));
                    break;
                case "dimension":
                    result.put("data", getDimensionInfo(mc));
                    break;
                case "all":
                    Map<String, Object> allInfo = new HashMap<>();
                    allInfo.put("position", getPositionInfo(mc.player));
                    allInfo.put("health", getHealthInfo(mc.player));
                    allInfo.put("dimension", getDimensionInfo(mc));
                    allInfo.put("gamemode", mc.interactionManager != null ? mc.interactionManager.getCurrentGameMode().getName() : "unknown");
                    result.put("data", allInfo);
                    break;
                default:
                    return createErrorResult("Unknown info type: " + infoType + ". Use 'position', 'health', 'dimension', or 'all'");
            }

            return GSON.toJson(result);

        } catch (Exception e) {
            Wynnutils.LOGGER.error("Error getting Minecraft info", e);
            return createErrorResult("Failed to get Minecraft info: " + e.getMessage());
        }
    }

    private Map<String, Object> getPositionInfo(PlayerEntity player) {
        Map<String, Object> pos = new HashMap<>();
        BlockPos blockPos = player.getBlockPos();
        pos.put("x", blockPos.getX());
        pos.put("y", blockPos.getY());
        pos.put("z", blockPos.getZ());
        pos.put("exact_x", player.getX());
        pos.put("exact_y", player.getY());
        pos.put("exact_z", player.getZ());
        return pos;
    }

    private Map<String, Object> getHealthInfo(PlayerEntity player) {
        Map<String, Object> health = new HashMap<>();
        health.put("health", player.getHealth());
        health.put("max_health", player.getMaxHealth());
        health.put("food_level", player.getHungerManager().getFoodLevel());
        health.put("saturation", player.getHungerManager().getSaturationLevel());
        return health;
    }

    private Map<String, Object> getDimensionInfo(MinecraftClient mc) {
        Map<String, Object> dimension = new HashMap<>();
        if (mc.world != null) {
            dimension.put("dimension", mc.world.getRegistryKey().getValue().toString());
            dimension.put("time_of_day", mc.world.getTimeOfDay());
            dimension.put("is_day", mc.world.isDay());
            dimension.put("is_raining", mc.world.isRaining());
            dimension.put("is_thundering", mc.world.isThundering());
        }
        return dimension;
    }

    private String createErrorResult(String message) {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "error");
        result.put("message", message);
        return GSON.toJson(result);
    }
}
