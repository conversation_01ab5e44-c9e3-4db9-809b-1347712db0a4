package com.ifallious.utils.ai.tools.impl;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.ifallious.utils.ai.tools.Tool;
import com.ifallious.utils.ai.tools.ToolExecute;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class RaidAspectTool implements Tool {
    private static final Gson GSON = new Gson();

    @Override
    public String getName() {
        return "get_raid_aspects";
    }
    @Override
    public String getDescription() {
        return """
                Gets the latest Aspects in all 4 raids this Week.
                Returns all Raids in short form:
                "TNA" The Nameless Anomaly,
                "TCC" The Canyon Colossos,
                "NOL" Nexus of Light,
                "NOTG" Nest of the Grootslang
                each of these contain their "Mythic" "Fabled" and "Legendary" rarity aspects in an Array
                """;
    }

    @ToolExecute
    public String execute(
    ) throws IOException {
        try {
            String url = "https://nori.fish/api/aspects";
            OkHttpClient client = new OkHttpClient();
            Request request = new Request.Builder()
                    .url(url)
                    .build();
            Response response = client.newCall(request).execute();
            JsonObject jsonObject = JsonParser.parseString(response.body().string()).getAsJsonObject();
            return GSON.toJson(jsonObject.get("Loot"));
        } catch (Exception e) {
            return createErrorResult("Failed to fetch latest news: " + e.getMessage());
        }
    }
    private String createErrorResult(String message) {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "error");
        result.put("message", message);
        return GSON.toJson(result);
    }
}
