package com.ifallious.utils.ai.json;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.ifallious.utils.ai.model.AIMessage;
import com.ifallious.Wynnutils;

import java.util.ArrayList;
import java.util.List;

/**
 * Parser for handling tool calls in AI responses.
 * Extracts tool calls from OpenAI-compatible JSON responses.
 */
public class ToolCallParser {
    private static final Gson GSON = new Gson();

    /**
     * Parse the full response and extract either content or tool calls
     */
    public static ParsedResponse parseResponse(String jsonBody) {
        try {
            JsonObject response = GSON.fromJson(jsonBody, JsonObject.class);
            
            if (!response.has("choices") || response.getAsJsonArray("choices").isEmpty()) {
                throw new RuntimeException("Invalid response: no choices found");
            }
            
            JsonObject choice = response.getAsJsonArray("choices").get(0).getAsJsonObject();
            JsonObject message = choice.getAsJsonObject("message");
            String finishReason = choice.has("finish_reason") ? choice.get("finish_reason").getAsString() : null;
            
            ParsedResponse result = new ParsedResponse();
            result.finishReason = finishReason;
            
            // Check if this is a tool call response
            if ("tool_calls".equals(finishReason) && message.has("tool_calls")) {
                result.hasToolCalls = true;
                result.toolCalls = parseToolCalls(message.getAsJsonArray("tool_calls"));
                result.content = message.has("content") && !message.get("content").isJsonNull() 
                    ? message.get("content").getAsString() : null;
            } else {
                // Regular text response
                result.hasToolCalls = false;
                result.content = message.has("content") && !message.get("content").isJsonNull() 
                    ? message.get("content").getAsString() : "";
            }
            
            return result;
            
        } catch (Exception e) {
            Wynnutils.LOGGER.error("Failed to parse AI response", e);
            ParsedResponse errorResult = new ParsedResponse();
            errorResult.hasToolCalls = false;
            errorResult.content = "Error parsing AI response: " + e.getMessage();
            return errorResult;
        }
    }
    
    private static List<AIMessage.ToolCall> parseToolCalls(JsonArray toolCallsArray) {
        List<AIMessage.ToolCall> toolCalls = new ArrayList<>();
        
        for (JsonElement element : toolCallsArray) {
            JsonObject toolCallObj = element.getAsJsonObject();
            
            AIMessage.ToolCall toolCall = new AIMessage.ToolCall();
            toolCall.id = toolCallObj.get("id").getAsString();
            toolCall.type = toolCallObj.get("type").getAsString();
            
            if (toolCallObj.has("function")) {
                JsonObject functionObj = toolCallObj.getAsJsonObject("function");
                toolCall.function = new AIMessage.ToolCall.Function();
                toolCall.function.name = functionObj.get("name").getAsString();
                toolCall.function.arguments = functionObj.get("arguments").getAsString();
            }
            
            toolCalls.add(toolCall);
        }
        
        return toolCalls;
    }
    
    /**
     * Result of parsing an AI response
     */
    public static class ParsedResponse {
        public boolean hasToolCalls;
        public String content;
        public List<AIMessage.ToolCall> toolCalls;
        public String finishReason;
        
        public AIMessage toAIMessage() {
            AIMessage message = new AIMessage("assistant", content);
            if (hasToolCalls) {
                message.tool_calls = toolCalls;
            }
            return message;
        }
    }
}
