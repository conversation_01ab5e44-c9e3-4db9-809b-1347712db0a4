package com.ifallious.utils.ai.json;

import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.ifallious.utils.ai.core.AIClientBase;
import com.ifallious.utils.ai.model.ChatResponse;

/**
 * Helpers for request/response JSON handling.
 */
public class Chat<PERSON><PERSON> extends AIClientBase {
    private static final Gson GSON = new Gson();

    public static String extractContent(String jsonBody) throws AIClientException {
        try {
            ChatResponse resp = GSON.fromJson(jsonBody, ChatResponse.class);
            if (resp != null && resp.choices != null && !resp.choices.isEmpty()) {
                ChatResponse.Choice c0 = resp.choices.get(0);
                if (c0 != null && c0.message != null && c0.message.content != null) {
                    return c0.message.content;
                }
            }
            JsonObject obj = GSON.fromJson(jsonBody, JsonObject.class);
            if (obj != null && obj.has("choices")) {
                var arr = obj.getAsJsonArray("choices");
                if (arr.size() > 0) {
                    JsonObject choiceObj = arr.get(0).getAsJsonObject();
                    if (choiceObj.has("message")) {
                        JsonObject msg = choiceObj.getAsJsonObject("message");
                        JsonElement contentEl = msg.get("content");
                        if (contentEl != null && !contentEl.isJsonNull()) return contentEl.getAsString();
                    }
                }
            }
            throw new AIClientException("Malformed API response: missing content");
        } catch (AIClientException e) {
            throw e;
        } catch (Exception e) {
            throw new AIClientException("Failed to parse response JSON: " + e.getMessage());
        }
    }
}

