package com.ifallious.utils.ai.model;

import java.util.List;

/**
 * Minimal OpenAI-compatible response for our needs.
 */
public class ChatResponse {
    public List<Choice> choices;

    public static class Choice {
        public int index;
        public AIMessage message; // for non-stream response
        public Delta delta;       // for streaming chunks
        public String finish_reason;
    }

    public static class Delta {
        public String role;
        public String content;
    }
}

