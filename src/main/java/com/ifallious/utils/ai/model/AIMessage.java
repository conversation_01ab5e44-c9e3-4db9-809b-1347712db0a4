package com.ifallious.utils.ai.model;

import java.util.List;

/**
 * OpenAI-compatible chat message model.
 * Supports assistant tool_calls and tool role messages.
 */
public class AIMessage {
    public String role;
    public String content;

    // For tool role messages
    public String name; // optional
    public String tool_call_id; // for tool role messages

    // For assistant messages with tool calls
    public List<ToolCall> tool_calls;

    public AIMessage() {}
    public AIMessage(String role, String content) {
        this.role = role;
        this.content = content;
    }

    public static AIMessage system(String content) { return new AIMessage("system", content); }
    public static AIMessage user(String content) { return new AIMessage("user", content); }
    public static AIMessage assistant(String content) { return new AIMessage("assistant", content); }

    public static AIMessage tool(String content, String toolCallId) {
        AIMessage msg = new AIMessage("tool", content);
        msg.tool_call_id = toolCallId;
        return msg;
    }

    public static class ToolCall {
        public String id;
        public String type;
        public Function function;

        public static class Function {
            public String name;
            public String arguments;
        }
    }
}

