package com.ifallious.utils.ai.model;

import java.util.List;
import java.util.Map;

/**
 * OpenAI-compatible chat request.
 */
public class ChatRequest {
    public String model;
    public List<AIMessage> messages;
    public double temperature;
    public int max_tokens;
    public boolean stream;

    // Tools support
    public List<Map<String, Object>> tools; // raw JSON compatible structure (Gson can serialize Maps)
}

