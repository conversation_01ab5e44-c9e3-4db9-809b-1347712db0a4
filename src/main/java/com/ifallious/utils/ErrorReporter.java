package com.ifallious.utils;

import com.ifallious.Wynnutils;
import net.minecraft.client.MinecraftClient;

import java.io.IOException;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import org.apache.commons.lang3.exception.ExceptionUtils;

/**
 * Utility class for reporting errors and logs to external API
 */
public class ErrorReporter {
    private static final String API_URL = "https://kongoboys.vercel.app/api/logs";
    private static final ExecutorService executor = Executors.newSingleThreadExecutor();
    
    /**
     * Get the current mod version dynamically
     */
    private static String getModVersion() {
        return Wynnutils.VERSION;
    }
    
    /**
     * Report an error or log to the external API
     * @param username The username/player name
     * @param details Brief description of what happened
     * @param logContent Detailed log information
     */
    public static void reportError(String username, String details, String logContent) {
        CompletableFuture.runAsync(() -> {
            try {
                sendReport(username, details, logContent);
            } catch (Exception e) {
                // Log locally if API call fails
                System.err.println("Failed to send error report: " + e.getMessage());
            }
        }, executor);
    }
    
    /**
     * Report an error with default username (current player)
     * @param details Brief description of what happened
     * @param logContent Detailed log information
     */
    public static void reportError(String details, String logContent) {
        String username = getCurrentPlayerName();
        reportError(username, details, logContent);
    }
    
    /**
     * Report an error with exception details
     * @param username The username/player name
     * @param details Brief description of what happened
     * @param exception The exception that occurred
     */
    public static void reportError(String username, String details, Exception exception) {
        String logContent = buildExceptionLog(exception);
        reportError(username, details, logContent);
    }
    
    /**
     * Report an error with exception details using current player name
     * @param details Brief description of what happened
     * @param exception The exception that occurred
     */
    public static void reportError(String details, Exception exception) {
        String username = getCurrentPlayerName();
        reportError(username, details, exception);
    }
    
    /**
     * Send the actual HTTP request to the API
     */
    private static void sendReport(String username, String details, String logContent) throws IOException {
        String jsonPayload = buildJsonPayload(username, details, logContent);
        
        URL url = new URL(API_URL);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        
        try {
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("Accept", "application/json");
            connection.setDoOutput(true);
            connection.setConnectTimeout(10000); // 10 seconds
            connection.setReadTimeout(10000); // 10 seconds
            
            // Send the request
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonPayload.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }
            
            // Check response
            int responseCode = connection.getResponseCode();
            if (responseCode >= 200 && responseCode < 300) {
                System.out.println("Error report sent successfully");
            } else {
                System.err.println("Failed to send error report. Response code: " + responseCode);
            }
            
        } finally {
            connection.disconnect();
        }
    }
    
    /**
     * Build the JSON payload according to the required format
     */
    private static String buildJsonPayload(String username, String details, String logContent) {
        return String.format(
            "{\"username\": \"%s\", \"details\": \"%s\", \"version\": \"%s\", \"logContent\": \"%s\"}",
            escapeJson(username),
            escapeJson(details),
            getModVersion(),
            escapeJson(logContent)
        );
    }
    
    /**
     * Escape special characters for JSON
     */
    private static String escapeJson(String input) {
        if (input == null) return "";
        return input.replace("\\", "\\\\")
                   .replace("\"", "\\\"")
                   .replace("\n", "\\n")
                   .replace("\r", "\\r")
                   .replace("\t", "\\t");
    }
    
    /**
     * Build detailed log from exception
     */
    private static String buildExceptionLog(Exception exception) {
        StringBuilder log = new StringBuilder();
        log.append("Exception: ").append(exception.getClass().getSimpleName()).append("\n");
        log.append("Message: ").append(exception.getMessage()).append("\n");
        log.append("Stack trace:\n");
        
        for (StackTraceElement element : exception.getStackTrace()) {
            log.append("  at ").append(element.toString()).append("\n");
        }
        
        return log.toString();
    }
    
    /**
     * Get the current player's name
     */
    private static String getCurrentPlayerName() {
        MinecraftClient client = Wynnutils.mc;
        if (client != null && client.player != null) {
            return client.player.getName().getString();
        }
        return "Unknown";
    }
    
    /**
     * Shutdown the executor service (call this when the mod is shutting down)
     */
    public static void shutdown() {
        executor.shutdown();
    }
} 