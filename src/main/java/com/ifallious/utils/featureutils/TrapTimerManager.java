package com.ifallious.utils.featureutils;

import com.ifallious.Wynnutils;
import com.ifallious.event.GlobalEventBus;
import com.ifallious.event.TickEvent;
import com.ifallious.utils.ErrorReporter;
import com.ifallious.utils.render.RenderTrapTimer;
import gg.essential.universal.UMinecraft;
import meteordevelopment.orbit.EventHandler;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.Iterator;

/**
 * Manages visual timers for all active traps
 * Handles creation, updates, cleanup, and render distance optimization
 */
public class TrapTimerManager {

    // Storage for active timers - key format: "x,y,z"
    private static final ConcurrentHashMap<String, RenderTrapTimer> activeTimers = new ConcurrentHashMap<>();

    // Queue for timers pending removal (to avoid concurrent modification)
    private static final ConcurrentLinkedQueue<String> pendingRemovals = new ConcurrentLinkedQueue<>();

    // Configuration
    private static final double MAX_RENDER_DISTANCE = 64.0; // Maximum distance to show timers
    private static final int UPDATE_FREQUENCY_TICKS = 5; // Update every 5 ticks (4 times per second)
    private static final int TRAP_DURATION_SECONDS = 29; // Default trap duration

    // Tracking
    private static long lastUpdateTick = 0;
    private static boolean isInitialized = false;

    /**
     * Initialize the timer manager and subscribe to events
     */
    public static void initialize() {
        if (!isInitialized) {
            TrapTimerManager manager = new TrapTimerManager();
            GlobalEventBus.subscribe(manager);
            isInitialized = true;
            Wynnutils.LOGGER.info("TrapTimerManager initialized");
        }
    }

    /**
     * Private constructor for event subscription
     */
    private TrapTimerManager() {
        // Private constructor for singleton-like behavior
    }

    /**
     * Handle tick events for timer updates
     * @param event The tick event
     */
    @EventHandler
    public void onTick(TickEvent event) {
        try {
            long currentTick = event.getCurrentTick();

            // Only update every UPDATE_FREQUENCY_TICKS ticks
            if (currentTick - lastUpdateTick >= UPDATE_FREQUENCY_TICKS) {
                updateAllTimers();
                lastUpdateTick = currentTick;
            }

        } catch (Exception e) {
            Wynnutils.LOGGER.error("TrapTimerManager tick handling failed", e);
            ErrorReporter.reportError("TrapTimerManager tick handling failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
        }
    }

    /**
     * Create a new timer for a trap at the specified coordinates
     * @param x X coordinate of the trap
     * @param y Y coordinate of the trap
     * @param z Z coordinate of the trap
     */
    public static void createTimer(int x, int y, int z) {
        createTimer(x, y, z, TRAP_DURATION_SECONDS);
    }

    /**
     * Create a new timer for a trap with custom duration
     * @param x X coordinate of the trap
     * @param y Y coordinate of the trap
     * @param z Z coordinate of the trap
     * @param durationSeconds Duration in seconds
     */
    public static void createTimer(int x, int y, int z, int durationSeconds) {
        try {
            String coordinateKey = createCoordinateKey(x, y, z);

            // Remove existing timer if present
            removeTimer(x, y, z);

            // Create new timer
            RenderTrapTimer timer = new RenderTrapTimer(x, y, z, durationSeconds);
            activeTimers.put(coordinateKey, timer);

            Wynnutils.LOGGER.debug("Created trap timer for coordinates [{}, {}, {}] with duration {}s", x, y, z, durationSeconds);

        } catch (Exception e) {
            Wynnutils.LOGGER.error("Failed to create trap timer for coordinates [{}, {}, {}]", x, y, z, e);
            ErrorReporter.reportError("TrapTimerManager timer creation failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
        }
    }

    /**
     * Remove a timer for the specified coordinates
     * @param x X coordinate
     * @param y Y coordinate
     * @param z Z coordinate
     * @return true if timer was removed, false if no timer existed
     */
    public static boolean removeTimer(int x, int y, int z) {
        try {
            String coordinateKey = createCoordinateKey(x, y, z);
            RenderTrapTimer timer = activeTimers.remove(coordinateKey);

            if (timer != null) {
                timer.cleanup();
                Wynnutils.LOGGER.debug("Removed trap timer for coordinates [{}, {}, {}]", x, y, z);
                return true;
            }

            return false;

        } catch (Exception e) {
            Wynnutils.LOGGER.error("Failed to remove trap timer for coordinates [{}, {}, {}]", x, y, z, e);
            ErrorReporter.reportError("TrapTimerManager timer removal failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
            return false;
        }
    }

    /**
     * Update all active timers
     */
    private void updateAllTimers() {
        try {
            // Process pending removals first
            processPendingRemovals();

            // Update all active timers
            Iterator<ConcurrentHashMap.Entry<String, RenderTrapTimer>> iterator = activeTimers.entrySet().iterator();

            while (iterator.hasNext()) {
                ConcurrentHashMap.Entry<String, RenderTrapTimer> entry = iterator.next();
                RenderTrapTimer timer = entry.getValue();

                // Check render distance optimization
                if (shouldHideTimer(timer)) {
                    continue; // Skip update but don't remove timer
                }

                // Update timer and remove if expired
                if (!timer.update()) {
                    iterator.remove();
                    Wynnutils.LOGGER.debug("Timer expired and removed for coordinates {}", entry.getKey());
                }
            }

        } catch (Exception e) {
            Wynnutils.LOGGER.error("TrapTimerManager update failed", e);
            ErrorReporter.reportError("TrapTimerManager update failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
        }
    }

    /**
     * Check if a timer should be hidden due to distance
     * @param timer The timer to check
     * @return true if timer should be hidden
     */
    private boolean shouldHideTimer(RenderTrapTimer timer) {
        try {
            if (UMinecraft.getPlayer() == null) {
                return true; // Hide if no player
            }

            double distance = timer.getDistanceFromPlayer();
            return distance > MAX_RENDER_DISTANCE;

        } catch (Exception e) {
            return false; // Show timer if distance check fails
        }
    }

    /**
     * Process pending timer removals
     */
    private void processPendingRemovals() {
        String coordinateKey;
        while ((coordinateKey = pendingRemovals.poll()) != null) {
            RenderTrapTimer timer = activeTimers.remove(coordinateKey);
            if (timer != null) {
                timer.cleanup();
            }
        }
    }

    /**
     * Clear all active timers
     */
    public static void clearAllTimers() {
        try {
            for (RenderTrapTimer timer : activeTimers.values()) {
                timer.cleanup();
            }
            activeTimers.clear();
            pendingRemovals.clear();

            Wynnutils.LOGGER.info("Cleared all trap timers");

        } catch (Exception e) {
            Wynnutils.LOGGER.error("Failed to clear all trap timers", e);
            ErrorReporter.reportError("TrapTimerManager clear all failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
        }
    }

    /**
     * Get the number of active timers
     * @return Number of active timers
     */
    public static int getActiveTimerCount() {
        return activeTimers.size();
    }

    /**
     * Check if a timer exists for the specified coordinates
     * @param x X coordinate
     * @param y Y coordinate
     * @param z Z coordinate
     * @return true if timer exists
     */
    public static boolean hasTimer(int x, int y, int z) {
        String coordinateKey = createCoordinateKey(x, y, z);
        return activeTimers.containsKey(coordinateKey);
    }

    /**
     * Create a coordinate key for HashMap lookups
     * @param x X coordinate
     * @param y Y coordinate
     * @param z Z coordinate
     * @return String key in format "x,y,z"
     */
    private static String createCoordinateKey(int x, int y, int z) {
        return x + "," + y + "," + z;
    }

    /**
     * Get debug information about active timers
     * @return Debug string with timer information
     */
    public static String getDebugInfo() {
        StringBuilder info = new StringBuilder();
        info.append("Active Timers: ").append(activeTimers.size()).append("\n");

        for (ConcurrentHashMap.Entry<String, RenderTrapTimer> entry : activeTimers.entrySet()) {
            RenderTrapTimer timer = entry.getValue();
            int[] coords = timer.getTrapCoordinates();
            double distance = timer.getDistanceFromPlayer();

            info.append("  [").append(coords[0]).append(", ").append(coords[1]).append(", ").append(coords[2])
                .append("] - Distance: ").append(String.format("%.1f", distance)).append("\n");
        }

        return info.toString();
    }
}
