package com.ifallious.utils.featureutils;
import java.util.Collection;
import java.util.Optional;
import java.util.concurrent.LinkedBlockingQueue;

import com.ifallious.Wynnutils;
import com.ifallious.utils.ErrorReporter;
import com.ifallious.utils.minecraft.PacketUtils;
import com.wynntils.utils.type.CappedValue;
import com.wynntils.utils.wynn.InventoryUtils;
import net.minecraft.network.packet.Packet;
import net.minecraft.network.packet.c2s.play.ClientCommandC2SPacket;
import net.minecraft.network.packet.c2s.play.HandSwingC2SPacket;
import net.minecraft.util.Hand;
import net.minecraft.network.packet.c2s.play.PlayerInteractItemC2SPacket;
import com.wynntils.core.components.Models;
import com.wynntils.models.characterstats.type.PowderSpecialInfo;
import org.apache.commons.lang3.exception.ExceptionUtils;

public class SpellmacroUtils {
    private static final LinkedBlockingQueue<String> queue = new LinkedBlockingQueue<>();
    /**
     * Add a spell to the queue
     * @param spell The spell to add
     */
    public static void add(Collection<String> spell) {
        try {
            if (spell == null) {
                return;
            }
            queue.addAll(spell);
        } catch (Exception e) {
            ErrorReporter.reportError("SpellmacroUtils add failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
        }
    }
    /**
     * Get the next spell in the queue
     * @return The next spell in the queue
     */
    public static String get() {
        try {
            return queue.poll();
        } catch (Exception e) {
            ErrorReporter.reportError("SpellmacroUtils get failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
            return null;
        }
    }
    /**
     * Get the size of the queue
     * @return The size of the queue
     */
    public static int size() {
        return queue.size();
    }
    /**
     * Get the packet for a spell
     * @param spell The spell to get the packet for
     * @return The packet for the spell
     */
    public static Packet<?> getPacket(String spell) {
        switch (spell) {
            case "L":
                return new HandSwingC2SPacket(Hand.MAIN_HAND);
            case "R":
                return new PlayerInteractItemC2SPacket(Hand.MAIN_HAND, 0, Wynnutils.mc.player.getYaw(), Wynnutils.mc.player.getPitch());
            case ".":
                return getPowderMelee();
            default:
                return null;
        }
    }
    /**
     * Get the packet for a powder melee
     * @return The packet for a powder melee
     */
    private static Packet<?> getPowderMelee() {
        try {
            if (Wynnutils.mc.player == null) {
                return null;
            }
            CappedValue cooldown = Models.CharacterStats.getItemCooldownTicks(InventoryUtils.getItemInHand());
            double percentage = cooldown.getPercentage();
            if (percentage == 100.0) {
                if (shouldPowder()) {
                    if (!Wynnutils.mc.player.isSneaking()) {
                            PacketUtils.sendPacket(new ClientCommandC2SPacket(Wynnutils.mc.player, ClientCommandC2SPacket.Mode.PRESS_SHIFT_KEY ));
                    }
                    return new HandSwingC2SPacket(Hand.MAIN_HAND);
                } else {
                    if (Wynnutils.mc.player.isSneaking()) {
                            PacketUtils.sendPacket(new ClientCommandC2SPacket(Wynnutils.mc.player, ClientCommandC2SPacket.Mode.RELEASE_SHIFT_KEY ));
                    }
                    return new HandSwingC2SPacket(Hand.MAIN_HAND);
                }
            } else {
                String nextSpell = get();
                return nextSpell != null ? getPacket(nextSpell) : null;
            }
        } catch (Exception e) {
            ErrorReporter.reportError("SpellmacroUtils getPowderMelee failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
            return null;
        }
    }
    /**
     * Check if we should Sneak
     * @return True if we should Sneak, false otherwise
     */
    private static Boolean shouldPowder() {
        Optional<PowderSpecialInfo> info = Models.CharacterStats.getPowderSpecialInfo();
        if (info.isPresent()) {
            return info.get().charge() > 0.9;
        }
        return false;
    }
}
