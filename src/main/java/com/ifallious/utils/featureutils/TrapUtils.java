package com.ifallious.utils.featureutils;

import com.ifallious.Wynnutils;
import com.ifallious.event.GlobalEventBus;
import com.ifallious.event.TrapEvent;
import com.ifallious.utils.ErrorReporter;
import com.ifallious.utils.minecraft.Tick;
import meteordevelopment.orbit.EventHandler;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.util.concurrent.ConcurrentHashMap;

/**
 * Utility class for tracking and managing trap placements
 * Provides duplicate detection and automatic cleanup after 30 seconds
 */
public class TrapUtils {

    // Storage for active traps - key format: "x,y,z"
    private static final ConcurrentHashMap<String, TrapInfo> activeTrapMap = new ConcurrentHashMap<>();

    // Storage for scheduled cleanup tasks to prevent memory leaks
    private static final ConcurrentHashMap<String, Long> scheduledCleanupTasks = new ConcurrentHashMap<>();

    /**
     * Initialize the trap tracking system and subscribe to events
     */
    public TrapUtils() {
        GlobalEventBus.subscribe(this);
        TrapTimerManager.initialize();
    }

    /**
     * Handle trap placement events
     * @param event The trap event containing coordinates
     */
    @EventHandler
    public void onTrapPlacement(TrapEvent event) {
        try {
            int x = event.getX();
            int y = event.getY();
            int z = event.getZ();

            String coordinateKey = createCoordinateKey(x, y, z);

            if (activeTrapMap.containsKey(coordinateKey)) {
                return; // Don't add the duplicate trap
            }

            // Create new trap info with current timestamp
            TrapInfo trapInfo = new TrapInfo(x, y, z, System.currentTimeMillis());

            // Add trap to active tracking
            activeTrapMap.put(coordinateKey, trapInfo);

            // Create visual timer for the trap
            TrapTimerManager.createTimer(x, y, z, 30);

            // Schedule automatic cleanup after 30 seconds (600 ticks)
            scheduleCleanup(coordinateKey);

        } catch (Exception e) {
            Wynnutils.LOGGER.error("TrapUtils trap placement handling failed", e);
            ErrorReporter.reportError("TrapUtils trap placement handling failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
        }
    }

    /**
     * Create a coordinate key for HashMap lookups
     * @param x X coordinate
     * @param y Y coordinate
     * @param z Z coordinate
     * @return String key in format "x,y,z"
     */
    private static String createCoordinateKey(int x, int y, int z) {
        return x + "," + y + "," + z;
    }

    /**
     * Schedule automatic cleanup of a trap after 30 seconds
     * @param coordinateKey The coordinate key of the trap to clean up
     */
    private void scheduleCleanup(String coordinateKey) {
        try {
            // Schedule cleanup task for 30 seconds (600 ticks) from now
            long cleanupTick = Tick.now() + 620;

            Tick.schedule(620, () -> {
                try {
                    // Remove trap from active tracking
                    TrapInfo removedTrap = activeTrapMap.remove(coordinateKey);

                    // Remove visual timer if trap info exists
                    if (removedTrap != null) {
                        TrapTimerManager.removeTimer(removedTrap.getX(), removedTrap.getY(), removedTrap.getZ());
                    }

                    // Remove the scheduled task reference
                    scheduledCleanupTasks.remove(coordinateKey);

                } catch (Exception e) {
                    Wynnutils.LOGGER.error("TrapUtils cleanup task failed for coordinates: " + coordinateKey, e);
                    ErrorReporter.reportError("TrapUtils cleanup task failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
                }
            });

            // Track the scheduled cleanup task
            scheduledCleanupTasks.put(coordinateKey, cleanupTick);

        } catch (Exception e) {
            Wynnutils.LOGGER.error("TrapUtils cleanup scheduling failed for coordinates: " + coordinateKey, e);
            ErrorReporter.reportError("TrapUtils cleanup scheduling failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
        }
    }

    /**
     * Get the number of currently tracked traps
     * @return The number of active traps
     */
    public static int getActiveTrapCount() {
        return activeTrapMap.size();
    }

    /**
     * Check if a trap exists at the specified coordinates
     * @param x X coordinate
     * @param y Y coordinate
     * @param z Z coordinate
     * @return true if a trap exists at these coordinates, false otherwise
     */
    public static boolean hasTrapAt(int x, int y, int z) {
        String coordinateKey = createCoordinateKey(x, y, z);
        return activeTrapMap.containsKey(coordinateKey);
    }

    /**
     * Get trap information at the specified coordinates
     * @param x X coordinate
     * @param y Y coordinate
     * @param z Z coordinate
     * @return TrapInfo object if trap exists, null otherwise
     */
    public static TrapInfo getTrapAt(int x, int y, int z) {
        String coordinateKey = createCoordinateKey(x, y, z);
        return activeTrapMap.get(coordinateKey);
    }

    /**
     * Manually remove a trap from tracking (for testing or special cases)
     * @param x X coordinate
     * @param y Y coordinate
     * @param z Z coordinate
     * @return true if trap was removed, false if no trap existed at coordinates
     */
    public static boolean removeTrap(int x, int y, int z) {
        try {
            String coordinateKey = createCoordinateKey(x, y, z);
            TrapInfo removedTrap = activeTrapMap.remove(coordinateKey);

            // Remove visual timer
            TrapTimerManager.removeTimer(x, y, z);

            // Also remove any scheduled cleanup task
            scheduledCleanupTasks.remove(coordinateKey);

            if (removedTrap != null) {
                Wynnutils.LOGGER.debug("Manually removed trap at coordinates [" + x + ", " + y + ", " + z + "]");
                return true;
            }
            return false;

        } catch (Exception e) {
            Wynnutils.LOGGER.error("TrapUtils manual trap removal failed", e);
            ErrorReporter.reportError("TrapUtils manual trap removal failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
            return false;
        }
    }

    /**
     * Clear all tracked traps (for testing or reset purposes)
     */
    public static void clearAllTraps() {
        try {
            int trapCount = activeTrapMap.size();
            activeTrapMap.clear();
            scheduledCleanupTasks.clear();

            // Clear all visual timers
            TrapTimerManager.clearAllTimers();

            Wynnutils.LOGGER.info("Cleared " + trapCount + " tracked traps");

        } catch (Exception e) {
            Wynnutils.LOGGER.error("TrapUtils clear all traps failed", e);
            ErrorReporter.reportError("TrapUtils clear all traps failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
        }
    }

    /**
     * Inner class to store trap information
     */
    public static class TrapInfo {
        private final int x, y, z;
        private final long timestamp;

        /**
         * Create a new TrapInfo object
         * @param x X coordinate
         * @param y Y coordinate
         * @param z Z coordinate
         * @param timestamp Timestamp when the trap was placed (in milliseconds)
         */
        public TrapInfo(int x, int y, int z, long timestamp) {
            this.x = x;
            this.y = y;
            this.z = z;
            this.timestamp = timestamp;
        }

        /**
         * Get the X coordinate of the trap
         * @return X coordinate
         */
        public int getX() {
            return x;
        }

        /**
         * Get the Y coordinate of the trap
         * @return Y coordinate
         */
        public int getY() {
            return y;
        }

        /**
         * Get the Z coordinate of the trap
         * @return Z coordinate
         */
        public int getZ() {
            return z;
        }

        /**
         * Get the timestamp when the trap was placed
         * @return Timestamp in milliseconds
         */
        public long getTimestamp() {
            return timestamp;
        }

        /**
         * Get the age of the trap in seconds
         * @return Age in seconds since placement
         */
        public long getAgeInSeconds() {
            return (System.currentTimeMillis() - timestamp) / 1000;
        }

        /**
         * Get the remaining time until automatic cleanup in seconds
         * @return Remaining seconds until cleanup (0 if already expired)
         */
        public long getRemainingTimeInSeconds() {
            long ageInSeconds = getAgeInSeconds();
            long remainingSeconds = 30 - ageInSeconds;
            return Math.max(0, remainingSeconds);
        }

        @Override
        public String toString() {
            return "TrapInfo{x=" + x + ", y=" + y + ", z=" + z +
                   ", age=" + getAgeInSeconds() + "s, remaining=" + getRemainingTimeInSeconds() + "s}";
        }
    }
}
