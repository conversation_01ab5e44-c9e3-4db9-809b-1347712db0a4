import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ChatParser {
    public static void main(String[] args) {
        String input = "&b\uDAFF\uDFFC\uE006\uDAFF\uDFFF\uE002\uDAFF\uDFFE \uE060\uDAFF\uDFFF\uE042\uDAFF\uDFFF\uE043\uDAFF\uDFFF\uE041\uDAFF\uDFFF\uE030\uDAFF\uDFFF\uE043\uDAFF\uDFFF\uE034\uDAFF\uDFFF\uE036\uDAFF\uDFFF\uE038\uDAFF\uDFFF\uE042\uDAFF\uDFFF\uE043\uDAFF\uDFFF\uE062\uDAFF\uDFC4&0\uE012\uE013\uE011\uE000\uE013\uE004\uE006\uE008\uE012\uE013\uDB00\uDC02&b &3GoodGirlCeline:&b test test test test test test";

        // Regex: ignore color codes (& + hex) and weird symbols, then capture username and message
        Pattern pattern = Pattern.compile(
                "(?:&[0-9a-fk-orA-FK-OR]|\\p{So}|\\p{Cn})*" + // skip formatting
                        "\\s*&[0-9a-fk-orA-FK-OR]*([A-Za-z0-9_]+):&[0-9a-fk-orA-FK-OR]*\\s*(.*)"
        );

        Matcher matcher = pattern.matcher(input);
        if (matcher.find()) {
            String username = matcher.group(1);
            String message = matcher.group(2);
            System.out.println("Username: " + username);
            System.out.println("Message: " + message);
        }
    }
}
