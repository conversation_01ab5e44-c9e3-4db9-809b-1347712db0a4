plugins {
	id 'fabric-loom' version '1.10-SNAPSHOT'
	id 'maven-publish'
	id "org.jetbrains.kotlin.jvm" version "2.1.21"
}

version = project.mod_version
group = project.maven_group

base {
	archivesName = project.archives_base_name
}

repositories {
	// Add repositories to retrieve artifacts from in here.
	// You should only use this when depending on other mods because
	// Lo<PERSON> adds the essential maven repositories to download Minecraft and libraries from automatically.
	// See https://docs.gradle.org/current/userguide/declaring_repositories.html
	// for more information about repositories.
	repositories {
		maven { url = "https://pkgs.dev.azure.com/djtheredstoner/DevAuth/_packaging/public/maven/v1" }
		maven {
			url = "https://repo.essential.gg/repository/maven-public"
		}
		exclusiveContent {
			forRepository {
				maven {
					name = "Modrinth"
					url = "https://api.modrinth.com/maven"
				}
			}
			filter {
				includeGroup "maven.modrinth"
			}
		}
		maven {
			name = "meteor-maven"
			url = "https://maven.meteordev.org/releases"
		}
		maven {
			name = "meteor-maven-snapshots"
			url = uri("https://maven.meteordev.org/snapshots")
		}
	}
}

dependencies {
	// To change the versions see the gradle.properties file
	minecraft "com.mojang:minecraft:${project.minecraft_version}"
	mappings "net.fabricmc:yarn:${project.yarn_mappings}:v2"
	modImplementation "net.fabricmc:fabric-loader:${project.loader_version}"
	// OkHttp for HTTP client
	implementation(include('com.squareup.okhttp3:okhttp:4.12.0'))
	implementation(include("com.squareup.okio:okio:3.6.0"))
	implementation(include("com.fasterxml.jackson.core:jackson-databind:2.17.1"))
	implementation(include("com.fasterxml.jackson.core:jackson-core:2.17.1"))
	implementation(include("com.fasterxml.jackson.core:jackson-annotations:2.17.1"))

	// Fabric API. This is technically optional, but you probably want it anyway.
	implementation(include("meteordevelopment:orbit:0.2.3"))
	implementation(include("meteordevelopment:meteor-client:1.21.4-SNAPSHOT"))
	modImplementation "net.fabricmc.fabric-api:fabric-api:${project.fabric_version}"
	modImplementation(include("gg.essential:elementa:${project.elementa_version}"))
	modImplementation(include("gg.essential:universalcraft-1.21.4-fabric:${project.universal_craft_version}"))
	modImplementation("maven.modrinth:wynntils:${project.wynntils_version}")
	modRuntimeOnly("me.djtheredstoner:DevAuth-fabric:1.2.1")
}

processResources {
	inputs.property "version", project.version

	filesMatching("fabric.mod.json") {
		expand "version": inputs.properties.version
	}
}

tasks.withType(JavaCompile).configureEach {
	it.options.release = 21
}

tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).all {
	kotlinOptions {
		jvmTarget = 21
	}
}

java {
	// Loom will automatically attach sourcesJar to a RemapSourcesJar task and to the "build" task
	// if it is present.
	// If you remove this line, sources will not be generated.
	withSourcesJar()

	sourceCompatibility = JavaVersion.VERSION_21
	targetCompatibility = JavaVersion.VERSION_21
}

jar {
	inputs.property "archivesName", project.base.archivesName
	archiveBaseName.set("wynnutils")
	from("LICENSE") {
		rename { "${it}_${inputs.properties.archivesName}" }
	}
}

// configure the maven publication
publishing {
	publications {
		create("mavenJava", MavenPublication) {
			artifactId = project.archives_base_name
			from components.java
		}
	}

	// See https://docs.gradle.org/current/userguide/publishing_maven.html for information on how to set up publishing.
	repositories {
		// Add repositories to publish to here.
		// Notice: This block does NOT have the same function as the block in the top level.
		// The repositories here will be used for publishing your artifact, not for
		// retrieving dependencies.
	}
}
